#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复WebDriver连接问题
解决"由于目标计算机积极拒绝，无法连接"的错误
"""

import time
import os
import subprocess
import psutil
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

def kill_existing_chrome_processes():
    """杀死现有的Chrome进程"""
    print("🔄 清理现有Chrome进程...")
    
    killed_count = 0
    for proc in psutil.process_iter(['pid', 'name']):
        try:
            if 'chrome' in proc.info['name'].lower():
                proc.kill()
                killed_count += 1
                print(f"  已终止进程: {proc.info['name']} (PID: {proc.info['pid']})")
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass
    
    if killed_count > 0:
        print(f"✅ 已清理 {killed_count} 个Chrome进程")
        time.sleep(2)  # 等待进程完全终止
    else:
        print("✅ 未发现需要清理的Chrome进程")

def check_chrome_installation():
    """检查Chrome浏览器安装"""
    print("🔍 检查Chrome浏览器安装...")
    
    chrome_paths = [
        r"C:\Program Files\Google\Chrome\Application\chrome.exe",
        r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
        r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe".format(os.getenv('USERNAME')),
    ]
    
    for path in chrome_paths:
        if os.path.exists(path):
            print(f"✅ 找到Chrome浏览器: {path}")
            return path
    
    print("❌ 未找到Chrome浏览器")
    print("请安装Chrome浏览器: https://www.google.com/chrome/")
    return None

def test_chromedriver():
    """测试ChromeDriver"""
    print("🔧 测试ChromeDriver...")
    
    try:
        # 使用webdriver-manager自动管理ChromeDriver
        driver_path = ChromeDriverManager().install()
        print(f"✅ ChromeDriver路径: {driver_path}")
        
        # 测试ChromeDriver是否可执行
        result = subprocess.run([driver_path, "--version"], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ ChromeDriver版本: {result.stdout.strip()}")
            return driver_path
        else:
            print(f"❌ ChromeDriver测试失败: {result.stderr}")
            return None
            
    except Exception as e:
        print(f"❌ ChromeDriver测试异常: {e}")
        return None

def create_robust_webdriver():
    """创建稳定的WebDriver实例"""
    print("🚀 创建稳定的WebDriver实例...")
    
    try:
        # 清理现有进程
        kill_existing_chrome_processes()
        
        # 检查Chrome安装
        chrome_path = check_chrome_installation()
        if not chrome_path:
            return None
        
        # 测试ChromeDriver
        driver_path = test_chromedriver()
        if not driver_path:
            return None
        
        # 配置Chrome选项
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--disable-features=VizDisplayCompositor")
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-plugins")
        chrome_options.add_argument("--disable-images")
        chrome_options.add_argument("--disable-javascript")  # 临时禁用JS加快测试
        chrome_options.add_argument("--window-size=1920,1080")
        
        # 设置用户数据目录
        user_data_dir = os.path.join(os.getcwd(), "chrome_user_data")
        chrome_options.add_argument(f"--user-data-dir={user_data_dir}")
        
        # 创建Service
        service = Service(driver_path)
        
        print("正在启动Chrome浏览器...")
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        print("✅ WebDriver创建成功")
        
        # 测试基本功能
        print("测试基本导航...")
        driver.get("https://www.baidu.com")
        time.sleep(2)
        
        title = driver.title
        print(f"✅ 导航测试成功，页面标题: {title}")
        
        return driver
        
    except Exception as e:
        print(f"❌ WebDriver创建失败: {e}")
        return None

def test_tencent_meeting_access():
    """测试腾讯会议页面访问"""
    print("\n🌐 测试腾讯会议页面访问...")
    
    driver = create_robust_webdriver()
    if not driver:
        return False
    
    try:
        # 测试腾讯会议主页
        print("访问腾讯会议主页...")
        driver.get("https://meeting.tencent.com")
        time.sleep(3)
        
        title = driver.title
        print(f"✅ 主页访问成功，标题: {title}")
        
        # 测试录制文件页面
        test_url = "https://meeting.tencent.com/cw/lvm1Z7Gj4f"
        print(f"访问录制文件页面: {test_url}")
        driver.get(test_url)
        time.sleep(3)
        
        title = driver.title
        print(f"✅ 录制页面访问成功，标题: {title}")
        
        # 检查页面内容
        page_source = driver.page_source
        if "录制" in page_source:
            print("✅ 发现录制内容")
        else:
            print("⚠️ 未发现录制内容，可能需要登录")
        
        driver.quit()
        print("✅ 腾讯会议访问测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 腾讯会议访问测试失败: {e}")
        try:
            driver.quit()
        except:
            pass
        return False

def fix_port_conflicts():
    """修复端口冲突问题"""
    print("\n🔧 检查和修复端口冲突...")
    
    # 检查常用的Chrome调试端口
    debug_ports = [9222, 9223, 9224, 9225]
    
    for port in debug_ports:
        try:
            # 检查端口是否被占用
            for conn in psutil.net_connections():
                if conn.laddr.port == port:
                    print(f"⚠️ 端口 {port} 被进程 {conn.pid} 占用")
                    
                    # 尝试终止占用端口的进程
                    try:
                        proc = psutil.Process(conn.pid)
                        if 'chrome' in proc.name().lower():
                            proc.kill()
                            print(f"✅ 已终止占用端口 {port} 的Chrome进程")
                    except:
                        pass
        except:
            continue
    
    print("✅ 端口冲突检查完成")

def comprehensive_fix():
    """综合修复方案"""
    print("🔧 WebDriver连接问题综合修复")
    print("=" * 60)
    
    steps = [
        ("清理Chrome进程", kill_existing_chrome_processes),
        ("修复端口冲突", fix_port_conflicts),
        ("检查Chrome安装", check_chrome_installation),
        ("测试ChromeDriver", test_chromedriver),
        ("测试WebDriver创建", lambda: create_robust_webdriver() is not None),
        ("测试腾讯会议访问", test_tencent_meeting_access),
    ]
    
    success_count = 0
    for step_name, step_func in steps:
        print(f"\n{'='*20} {step_name} {'='*20}")
        try:
            result = step_func()
            if result or result is None:  # None表示无返回值但执行成功
                print(f"✅ {step_name}完成")
                success_count += 1
            else:
                print(f"❌ {step_name}失败")
        except Exception as e:
            print(f"❌ {step_name}异常: {e}")
    
    print(f"\n{'='*60}")
    print(f"修复结果: {success_count}/{len(steps)} 步骤成功")
    
    if success_count == len(steps):
        print("🎉 所有问题已修复！")
        print("现在可以正常使用下载器了")
        return True
    else:
        print("⚠️ 部分问题仍然存在")
        print("建议:")
        print("1. 重启计算机")
        print("2. 更新Chrome浏览器")
        print("3. 检查防火墙设置")
        print("4. 以管理员权限运行")
        return False

def main():
    """主函数"""
    print("🛠️ WebDriver连接问题修复工具")
    print("=" * 60)
    
    print("选择操作:")
    print("1. 综合修复（推荐）")
    print("2. 仅清理Chrome进程")
    print("3. 仅测试WebDriver")
    print("4. 仅测试腾讯会议访问")
    
    choice = input("请选择 (1-4): ").strip()
    
    if choice == '1':
        comprehensive_fix()
    elif choice == '2':
        kill_existing_chrome_processes()
    elif choice == '3':
        driver = create_robust_webdriver()
        if driver:
            driver.quit()
            print("✅ WebDriver测试成功")
        else:
            print("❌ WebDriver测试失败")
    elif choice == '4':
        test_tencent_meeting_access()
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
