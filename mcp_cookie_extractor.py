#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用MCP Chrome扩展获取腾讯会议cookies
"""

import json
import time
from cookie_manager import <PERSON>ieManager

def get_cookies_via_mcp_chrome():
    """
    使用MCP Chrome扩展获取cookies
    """
    print("使用MCP Chrome扩展获取cookies")
    print("=" * 40)
    
    try:
        # 首先导航到腾讯会议页面
        print("1. 导航到腾讯会议页面...")
        
        # 使用chrome-mcp-stdio导航到页面
        # 这里需要用户先在浏览器中登录
        
        # 注入JavaScript获取cookies
        print("2. 注入JavaScript获取cookies...")
        
        cookie_script = """
        // 获取所有cookies
        function extractAllCookies() {
            const cookies = [];
            
            // 获取document.cookie中的cookies
            const cookieString = document.cookie;
            if (cookieString) {
                const pairs = cookieString.split(';');
                for (let pair of pairs) {
                    const [name, value] = pair.trim().split('=');
                    if (name && value) {
                        cookies.push({
                            name: name.trim(),
                            value: decodeURIComponent(value.trim()),
                            domain: window.location.hostname,
                            path: '/',
                            secure: window.location.protocol === 'https:',
                            httpOnly: false,
                            sameSite: 'Lax'
                        });
                    }
                }
            }
            
            // 尝试获取更多cookie信息
            const result = {
                cookies: cookies,
                domain: window.location.hostname,
                url: window.location.href,
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent,
                cookieCount: cookies.length
            };
            
            // 在控制台输出结果
            console.log('提取的Cookies:', result);
            
            // 创建一个隐藏元素存储结果
            let resultDiv = document.getElementById('mcp-cookie-result');
            if (!resultDiv) {
                resultDiv = document.createElement('div');
                resultDiv.id = 'mcp-cookie-result';
                resultDiv.style.display = 'none';
                document.body.appendChild(resultDiv);
            }
            resultDiv.textContent = JSON.stringify(result, null, 2);
            
            // 也尝试复制到剪贴板
            if (navigator.clipboard) {
                navigator.clipboard.writeText(JSON.stringify(result, null, 2))
                    .then(() => console.log('Cookies已复制到剪贴板'))
                    .catch(err => console.log('复制到剪贴板失败:', err));
            }
            
            return result;
        }
        
        // 执行提取
        const cookieData = extractAllCookies();
        
        // 触发自定义事件，通知MCP
        window.dispatchEvent(new CustomEvent('mcpCookiesExtracted', {
            detail: cookieData
        }));
        
        cookieData;
        """
        
        return cookie_script
        
    except Exception as e:
        print(f"✗ MCP cookie提取失败: {e}")
        return None

def setup_mcp_cookie_listener():
    """
    设置MCP cookie监听器
    """
    listener_script = """
    // 监听cookie提取事件
    window.addEventListener('mcpCookiesExtracted', function(event) {
        console.log('MCP Cookie提取完成:', event.detail);
        
        // 发送消息给MCP
        if (window.chrome && window.chrome.runtime) {
            chrome.runtime.sendMessage({
                type: 'COOKIES_EXTRACTED',
                data: event.detail
            });
        }
    });
    
    // 设置全局变量供MCP访问
    window.mcpCookieData = null;
    
    console.log('MCP Cookie监听器已设置');
    """
    
    return listener_script

def extract_cookies_interactive():
    """
    交互式cookie提取
    """
    print("交互式Cookie提取")
    print("=" * 40)
    print("请按以下步骤操作:")
    print()
    print("1. 确保您已在浏览器中打开腾讯会议页面并登录")
    print("2. 打开浏览器开发者工具 (F12)")
    print("3. 切换到 Console (控制台) 标签")
    print("4. 复制以下JavaScript代码并粘贴到控制台:")
    print()
    
    script = get_cookies_via_mcp_chrome()
    print("```javascript")
    print(script)
    print("```")
    print()
    print("5. 按回车执行代码")
    print("6. 代码执行后，cookies将被复制到剪贴板")
    print("7. 返回此程序，选择粘贴cookies选项")
    print()
    
    input("准备好后按回车继续...")
    
    # 提供粘贴选项
    print("\n现在请粘贴从浏览器获取的cookie数据:")
    print("(可以直接粘贴JSON数据，输入完成后按回车)")
    
    try:
        cookie_json = input("Cookie JSON: ").strip()
        
        if cookie_json:
            cookie_data = json.loads(cookie_json)
            
            manager = CookieManager()
            
            # 保存cookies
            save_data = {
                'cookies': cookie_data.get('cookies', []),
                'saved_time': cookie_data.get('timestamp', ''),
                'domain': cookie_data.get('domain', 'meeting.tencent.com'),
                'source': 'mcp_interactive'
            }
            
            with open(manager.cookie_path, 'w', encoding='utf-8') as f:
                json.dump(save_data, f, ensure_ascii=False, indent=2)
            
            print(f"✓ 成功保存 {len(cookie_data.get('cookies', []))} 个cookies")
            return True
        else:
            print("✗ 未输入cookie数据")
            return False
            
    except json.JSONDecodeError:
        print("✗ JSON格式错误，请检查输入的数据")
        return False
    except Exception as e:
        print(f"✗ 保存失败: {e}")
        return False

def auto_extract_with_mcp():
    """
    尝试自动通过MCP提取cookies
    """
    print("自动MCP Cookie提取")
    print("=" * 40)
    
    try:
        # 这里需要实际的MCP Chrome扩展集成
        print("正在尝试通过MCP自动提取cookies...")
        
        # 模拟MCP调用
        print("⚠️ 自动提取功能需要MCP Chrome扩展支持")
        print("请使用交互式提取方式")
        
        return False
        
    except Exception as e:
        print(f"✗ 自动提取失败: {e}")
        return False

def validate_extracted_cookies():
    """
    验证提取的cookies
    """
    print("\n验证提取的cookies")
    print("=" * 30)
    
    try:
        manager = CookieManager()
        info = manager.get_cookie_info()
        
        if not info:
            print("✗ 未找到cookies文件")
            return False
        
        print(f"Cookie信息:")
        print(f"  数量: {info['cookie_count']}")
        print(f"  保存时间: {info['saved_time']}")
        print(f"  有效性: {'有效' if info['is_valid'] else '已过期'}")
        
        # 尝试使用cookies访问页面
        print("\n测试cookies有效性...")
        
        from tencent_meeting_downloader import TencentMeetingDownloader
        
        downloader = TencentMeetingDownloader(
            headless=False,
            use_cookies=True
        )
        
        test_url = "https://meeting.tencent.com/cw/lvm1Z7Gj4f"
        success = downloader.open_meeting_page(test_url)
        
        if success:
            print("✓ Cookies验证成功！可以正常访问页面")
        else:
            print("✗ Cookies可能无效，建议重新获取")
        
        downloader.close()
        return success
        
    except Exception as e:
        print(f"✗ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("MCP Cookie提取工具")
    print("=" * 50)
    
    print("选择操作:")
    print("1. 交互式提取cookies (推荐)")
    print("2. 显示JavaScript提取代码")
    print("3. 验证现有cookies")
    print("4. 清除cookies")
    
    choice = input("\n请选择 (1-4): ").strip()
    
    if choice == '1':
        extract_cookies_interactive()
        validate_extracted_cookies()
    elif choice == '2':
        script = get_cookies_via_mcp_chrome()
        print("\nJavaScript提取代码:")
        print("```javascript")
        print(script)
        print("```")
    elif choice == '3':
        validate_extracted_cookies()
    elif choice == '4':
        manager = CookieManager()
        manager.clear_cookies()
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
