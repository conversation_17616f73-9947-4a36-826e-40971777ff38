#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
腾讯会议录制内容下载器
支持下载视频、音频、转写文本和会议纪要
"""

import time
import os
import requests
import logging
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
import json
from urllib.parse import urlparse, unquote
from pathlib import Path
from config import get_config
from cookie_manager import CookieManager

class TencentMeetingDownloader:
    def __init__(self, headless=False, download_dir=None, use_cookies=True):
        """
        初始化下载器

        Args:
            headless (bool): 是否使用无头模式
            download_dir (str): 下载目录，默认为当前目录
            use_cookies (bool): 是否使用保存的cookies
        """
        self.config = get_config()
        # 如果没有指定下载目录，使用当前工作目录
        self.download_dir = download_dir or os.getcwd()
        self.use_cookies = use_cookies
        self.cookie_manager = CookieManager()
        self.ensure_download_dir()
        self.setup_logging()
        self.setup_driver(headless)

    def setup_logging(self):
        """设置日志"""
        log_dir = os.path.join(self.download_dir, "logs")
        os.makedirs(log_dir, exist_ok=True)

        log_file = os.path.join(log_dir, f"download_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")

        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def setup_driver(self, headless):
        """设置Chrome浏览器驱动"""
        chrome_options = Options()

        # 设置下载目录
        prefs = {
            "download.default_directory": self.download_dir,
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True,
            "profile.default_content_settings.popups": 0,
            "profile.default_content_setting_values.automatic_downloads": 1
        }
        chrome_options.add_experimental_option("prefs", prefs)

        if headless:
            chrome_options.add_argument("--headless")

        # 添加配置中的Chrome选项
        for option in self.config.CHROME_OPTIONS:
            chrome_options.add_argument(option)

        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.wait = WebDriverWait(self.driver, self.config.BROWSER_TIMEOUT)
            self.logger.info("Chrome浏览器驱动初始化成功")
        except WebDriverException as e:
            self.logger.error(f"Chrome浏览器驱动初始化失败: {e}")
            raise
    
    def ensure_download_dir(self):
        """确保下载目录存在"""
        Path(self.download_dir).mkdir(parents=True, exist_ok=True)
        # 如果logger还没有初始化，就直接打印
        if hasattr(self, 'logger'):
            self.logger.info(f"下载目录: {self.download_dir}")
        else:
            print(f"下载目录: {self.download_dir}")
    
    def open_meeting_page(self, url):
        """
        打开腾讯会议页面

        Args:
            url (str): 腾讯会议录制页面URL
        """
        self.logger.info(f"正在打开页面: {url}")

        # 改进的导航流程：先确保登录状态，再跳转到目标页面
        try:
            # 如果启用cookies，先加载cookies并确保登录状态
            if self.use_cookies and self.cookie_manager.is_cookies_valid():
                self.logger.info("检测到有效cookies，正在加载...")
                if self.cookie_manager.load_cookies(self.driver):
                    self.logger.info("Cookies加载成功")

                    # 先访问主页确保cookies生效
                    self.driver.get("https://meeting.tencent.com")
                    time.sleep(2)

                    # 检查登录状态
                    page_source = self.driver.page_source
                    if "登录" in page_source or "扫码" in page_source:
                        self.logger.warning("Cookies可能已过期，需要重新登录")
                    else:
                        self.logger.info("登录状态确认成功")

                    # 然后跳转到目标页面
                    self.logger.info(f"跳转到目标页面: {url}")
                    self.driver.get(url)
                    time.sleep(3)
                else:
                    self.logger.warning("Cookies加载失败，直接访问目标页面")
                    self.driver.get(url)
            else:
                # 如果没有有效cookies，直接访问页面
                self.logger.info("未检测到有效cookies，直接访问页面")
                self.driver.get(url)

        except Exception as e:
            self.logger.error(f"页面导航过程中出错: {e}")
            # 如果导航出错，尝试直接访问目标页面
            self.driver.get(url)

        # 等待页面加载完成
        try:
            # 先等待页面基本加载
            time.sleep(5)  # 增加等待时间

            # 检查是否需要登录（更宽松的检查）
            if self.check_login_required():
                self.logger.warning("页面可能需要登录，但继续尝试...")
                # 不直接返回False，而是继续尝试

            # 尝试等待另存为按钮出现（更宽松的等待）
            try:
                self.wait.until(EC.presence_of_element_located((By.CLASS_NAME, "saveas-btn")))
                self.logger.info("页面加载完成，找到另存为按钮")
                button_found = True
            except TimeoutException:
                # 如果没找到标准按钮，尝试查找其他可能的下载按钮
                self.logger.warning("未找到标准另存为按钮，尝试查找其他下载按钮...")
                button_found = self.find_alternative_download_button()

            # 如果启用cookies，保存当前cookies
            if self.use_cookies:
                self.cookie_manager.save_cookies(self.driver)

            if button_found:
                self.logger.info("页面准备就绪，可以进行下载")
                return True
            else:
                self.logger.warning("未找到下载按钮，但页面已加载")
                return True  # 仍然返回True，让用户手动操作

        except Exception as e:
            self.logger.error(f"页面加载过程中出现错误: {e}")
            # 检查是否是权限问题
            if self.check_permission_denied():
                self.logger.error("访问被拒绝，可能需要登录或权限")
            return True  # 即使有错误也返回True，让用户手动检查

    def find_alternative_download_button(self):
        """
        查找替代的下载按钮

        Returns:
            bool: 是否找到下载按钮
        """
        try:
            # 尝试多种可能的下载按钮选择器
            button_selectors = [
                (By.XPATH, "//button[contains(text(), '另存为')]"),
                (By.XPATH, "//button[contains(text(), '下载')]"),
                (By.XPATH, "//div[contains(@class, 'saveas')]"),
                (By.XPATH, "//div[contains(@class, 'download')]"),
                (By.XPATH, "//a[contains(text(), '下载')]"),
                (By.XPATH, "//span[contains(text(), '另存为')]"),
                (By.CSS_SELECTOR, "[class*='save']"),
                (By.CSS_SELECTOR, "[class*='download']"),
            ]

            for selector_type, selector_value in button_selectors:
                try:
                    elements = self.driver.find_elements(selector_type, selector_value)
                    if elements:
                        for element in elements:
                            if element.is_displayed():
                                self.logger.info(f"找到替代下载按钮: {selector_value}")
                                return True
                except Exception as e:
                    continue

            self.logger.warning("未找到任何下载按钮")
            return False

        except Exception as e:
            self.logger.error(f"查找替代下载按钮失败: {e}")
            return False

    def check_login_required(self):
        """
        检查页面是否需要登录

        Returns:
            bool: 是否需要登录
        """
        try:
            page_source = self.driver.page_source
            login_indicators = [
                "登录", "login", "请先登录", "未登录",
                "sign in", "authentication required"
            ]

            for indicator in login_indicators:
                if indicator.lower() in page_source.lower():
                    return True

            # 检查是否有登录按钮
            try:
                self.driver.find_element(By.XPATH, "//button[contains(text(), '登录')]")
                return True
            except:
                pass

            return False

        except Exception as e:
            self.logger.error(f"检查登录状态失败: {e}")
            return False

    def check_permission_denied(self):
        """
        检查是否权限被拒绝

        Returns:
            bool: 是否权限被拒绝
        """
        try:
            page_source = self.driver.page_source
            permission_indicators = [
                "权限", "permission", "访问被拒绝", "access denied",
                "无权限", "unauthorized", "forbidden", "过期", "expired"
            ]

            for indicator in permission_indicators:
                if indicator.lower() in page_source.lower():
                    return True

            return False

        except Exception as e:
            self.logger.error(f"检查权限状态失败: {e}")
            return False

    def manual_login_mode(self):
        """
        手动登录模式
        打开浏览器让用户手动登录，然后保存cookies
        """
        self.logger.info("启动手动登录模式...")

        try:
            # 打开腾讯会议主页
            self.driver.get("https://meeting.tencent.com")

            print("\n" + "="*50)
            print("手动登录模式")
            print("="*50)
            print("1. 浏览器已打开腾讯会议页面")
            print("2. 请在浏览器中手动登录您的账号")
            print("3. 登录完成后，按回车键继续...")
            print("4. 程序将自动保存您的登录状态")
            print("="*50)

            input("登录完成后按回车键继续...")

            # 保存cookies
            if self.cookie_manager.save_cookies(self.driver):
                print("✓ 登录状态已保存，下次使用时将自动登录")
                return True
            else:
                print("✗ 保存登录状态失败")
                return False

        except Exception as e:
            self.logger.error(f"手动登录模式失败: {e}")
            return False
    
    def click_save_as_button(self):
        """点击另存为按钮（改进版，支持多种按钮类型）"""
        try:
            # 首先尝试标准的另存为按钮
            try:
                save_as_btn = self.wait.until(
                    EC.element_to_be_clickable((By.CLASS_NAME, "saveas-btn"))
                )
                save_as_btn.click()
                self.logger.info("已点击标准另存为按钮")
                button_clicked = True
            except TimeoutException:
                # 如果标准按钮不可用，尝试其他按钮
                self.logger.warning("标准另存为按钮不可用，尝试其他按钮...")
                button_clicked = self.click_alternative_download_button()

            if not button_clicked:
                self.logger.error("未能点击任何下载按钮")
                return False

            # 等待下拉菜单出现（更宽松的等待）
            try:
                self.wait.until(
                    EC.presence_of_element_located((By.CLASS_NAME, "dropdown-for-download"))
                )
                self.logger.info("下拉菜单已展开")
            except TimeoutException:
                self.logger.warning("未检测到标准下拉菜单，但继续尝试...")

            time.sleep(2)  # 增加等待时间
            return True

        except Exception as e:
            self.logger.error(f"点击另存为按钮失败: {e}")
            return False

    def click_alternative_download_button(self):
        """
        点击替代的下载按钮

        Returns:
            bool: 是否成功点击按钮
        """
        try:
            # 尝试多种可能的下载按钮
            button_selectors = [
                (By.XPATH, "//button[contains(text(), '另存为')]"),
                (By.XPATH, "//button[contains(text(), '下载')]"),
                (By.XPATH, "//div[contains(@class, 'saveas')]"),
                (By.XPATH, "//div[contains(@class, 'download')]"),
                (By.XPATH, "//a[contains(text(), '下载')]"),
                (By.XPATH, "//span[contains(text(), '另存为')]"),
            ]

            for selector_type, selector_value in button_selectors:
                try:
                    elements = self.driver.find_elements(selector_type, selector_value)
                    for element in elements:
                        if element.is_displayed() and element.is_enabled():
                            element.click()
                            self.logger.info(f"成功点击替代下载按钮: {selector_value}")
                            time.sleep(1)
                            return True
                except Exception as e:
                    continue

            self.logger.warning("未找到可点击的下载按钮")
            return False

        except Exception as e:
            self.logger.error(f"点击替代下载按钮失败: {e}")
            return False

        except TimeoutException:
            self.logger.error("找不到另存为按钮或下拉菜单")
            raise
    
    def download_video_content(self):
        """下载视频内容"""
        try:
            self.logger.info("正在下载视频内容...")
            # 找到视频内容选项
            video_option = self.driver.find_element(
                By.XPATH, "//li[text()='视频内容']"
            )
            video_option.click()
            self.logger.info("已点击视频内容下载")
            return True
        except NoSuchElementException:
            self.logger.error("找不到视频内容选项")
            return False

    def download_audio_content(self):
        """下载纯音频文件"""
        try:
            self.logger.info("正在下载纯音频文件...")
            audio_option = self.driver.find_element(
                By.XPATH, "//li[text()='纯音频文件']"
            )
            audio_option.click()
            self.logger.info("已点击纯音频文件下载")
            return True
        except NoSuchElementException:
            self.logger.error("找不到纯音频文件选项")
            return False
    
    def download_transcript(self, version="original"):
        """
        下载转写文本

        Args:
            version (str): 版本类型，"original"(原文版) 或 "optimized"(智能优化版)
        """
        try:
            self.logger.info(f"正在下载转写文本({version})...")

            # 先点击转写文本主菜单
            transcript_menu = self.driver.find_element(
                By.XPATH, "//li[contains(text(), '转写文本')]"
            )
            transcript_menu.click()
            time.sleep(0.5)

            # 选择版本
            if version == "original":
                option = self.driver.find_element(
                    By.XPATH, "//li[text()='原文版']"
                )
            else:
                option = self.driver.find_element(
                    By.XPATH, "//li[text()='智能优化版']"
                )

            option.click()
            self.logger.info(f"已点击转写文本({version})下载")
            return True
        except NoSuchElementException:
            self.logger.error(f"找不到转写文本({version})选项")
            return False
    
    def download_summary(self, summary_type="topic"):
        """
        下载纪要文本
        
        Args:
            summary_type (str): 纪要类型
                - "topic": 主题摘要
                - "chapter": 分章节小结
                - "speaker": 发言人观点
                - "deepseek": DeepSeek摘要
        """
        try:
            type_map = {
                "topic": "主题摘要",
                "chapter": "分章节小结", 
                "speaker": "发言人观点",
                "deepseek": "DeepSeek摘要"
            }
            
            self.logger.info(f"正在下载纪要文本({type_map[summary_type]})...")

            # 先点击纪要文本主菜单
            summary_menu = self.driver.find_element(
                By.XPATH, "//li[contains(text(), '纪要文本')]"
            )
            summary_menu.click()
            time.sleep(0.5)

            # 选择具体类型
            option = self.driver.find_element(
                By.XPATH, f"//li[text()='{type_map[summary_type]}']"
            )
            option.click()
            self.logger.info(f"已点击纪要文本({type_map[summary_type]})下载")
            return True
        except NoSuchElementException:
            self.logger.error(f"找不到纪要文本({type_map.get(summary_type, summary_type)})选项")
            return False
    
    def save_to_my_recordings(self):
        """转存至我的录制"""
        try:
            self.logger.info("正在转存至我的录制...")
            save_option = self.driver.find_element(
                By.XPATH, "//li[contains(text(), '转存至\"我的录制\"')]"
            )
            save_option.click()
            self.logger.info("已点击转存至我的录制")
            return True
        except NoSuchElementException:
            self.logger.error("找不到转存至我的录制选项")
            return False
    
    def wait_for_download_complete(self, timeout=300):
        """
        等待下载完成

        Args:
            timeout (int): 超时时间（秒）
        """
        self.logger.info("等待下载完成...")
        start_time = time.time()

        while time.time() - start_time < timeout:
            # 检查下载目录中是否有.crdownload文件（Chrome下载中的临时文件）
            temp_files = list(Path(self.download_dir).glob("*.crdownload"))
            if not temp_files:
                self.logger.info("下载完成")
                return True

            time.sleep(2)

        self.logger.warning("下载超时")
        return False
    
    def download_all_content(self, url, content_types=None):
        """
        下载所有指定类型的内容
        
        Args:
            url (str): 腾讯会议录制页面URL
            content_types (list): 要下载的内容类型列表
                可选值: ['video', 'audio', 'transcript_original', 'transcript_optimized',
                        'summary_topic', 'summary_chapter', 'summary_speaker', 'summary_deepseek']
        """
        if content_types is None:
            content_types = ['video', 'audio', 'transcript_original', 'summary_topic']
        
        try:
            self.open_meeting_page(url)
            
            for content_type in content_types:
                self.logger.info(f"开始下载: {content_type}")

                # 重新点击另存为按钮（每次下载后菜单会关闭）
                self.click_save_as_button()

                success = False
                if content_type == 'video':
                    success = self.download_video_content()
                elif content_type == 'audio':
                    success = self.download_audio_content()
                elif content_type == 'transcript_original':
                    success = self.download_transcript('original')
                elif content_type == 'transcript_optimized':
                    success = self.download_transcript('optimized')
                elif content_type == 'summary_topic':
                    success = self.download_summary('topic')
                elif content_type == 'summary_chapter':
                    success = self.download_summary('chapter')
                elif content_type == 'summary_speaker':
                    success = self.download_summary('speaker')
                elif content_type == 'summary_deepseek':
                    success = self.download_summary('deepseek')

                if success:
                    # 等待下载开始
                    time.sleep(3)
                    # 等待下载完成
                    self.wait_for_download_complete()

                time.sleep(2)  # 间隔时间

        except Exception as e:
            self.logger.error(f"下载过程中出现错误: {e}")
        finally:
            self.close()
    
    def close(self):
        """关闭浏览器"""
        try:
            if hasattr(self, 'driver') and self.driver:
                # 尝试优雅关闭
                try:
                    self.driver.quit()
                    self.logger.info("浏览器已关闭")
                except Exception as e:
                    # 如果优雅关闭失败，强制关闭
                    self.logger.warning(f"优雅关闭失败，尝试强制关闭: {e}")
                    try:
                        self.driver.close()
                        self.driver.quit()
                    except:
                        pass
                    self.logger.info("浏览器已强制关闭")
        except Exception as e:
            self.logger.error(f"关闭浏览器时出错: {e}")
        finally:
            # 确保driver引用被清除
            if hasattr(self, 'driver'):
                self.driver = None

def main():
    """主函数示例"""
    # 腾讯会议录制页面URL
    meeting_url = "https://meeting.tencent.com/cw/lvm1Z7Gj4f"
    
    # 创建下载器实例
    downloader = TencentMeetingDownloader(
        headless=False,  # 设置为True可以无头模式运行
        download_dir=None  # 使用当前目录
    )
    
    # 指定要下载的内容类型
    content_types = [
        'video',                # 视频内容
        'audio',                # 纯音频文件
        'transcript_original',  # 原文版转写
        'transcript_optimized', # 智能优化版转写
        'summary_topic',        # 主题摘要
        'summary_chapter',      # 分章节小结
        'summary_speaker',      # 发言人观点
        'summary_deepseek'      # DeepSeek摘要
    ]
    
    try:
        # 开始下载
        downloader.download_all_content(meeting_url, content_types)
    except KeyboardInterrupt:
        print("\n用户中断下载")
    except Exception as e:
        print(f"程序执行出错: {e}")
    finally:
        downloader.close()

if __name__ == "__main__":
    main()
