#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
腾讯会议录制内容下载器 - 安装脚本
自动安装所需依赖和配置环境
"""

import subprocess
import sys
import os
import platform

def run_command(command, description):
    """运行命令并显示结果"""
    print(f"\n正在{description}...")
    print(f"执行命令: {command}")
    
    try:
        result = subprocess.run(command, shell=True, check=True, 
                              capture_output=True, text=True, encoding='utf-8')
        print(f"✓ {description}成功")
        if result.stdout:
            print(f"输出: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ {description}失败")
        print(f"错误: {e}")
        if e.stdout:
            print(f"输出: {e.stdout}")
        if e.stderr:
            print(f"错误信息: {e.stderr}")
        return False

def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 7):
        print("✗ 需要Python 3.7或更高版本")
        return False
    
    print("✓ Python版本符合要求")
    return True

def check_pip():
    """检查pip是否可用"""
    print("\n检查pip...")
    try:
        import pip
        print("✓ pip已安装")
        return True
    except ImportError:
        print("✗ pip未安装")
        return False

def install_requirements():
    """安装Python依赖包"""
    print("\n=== 安装Python依赖包 ===")
    
    # 升级pip
    if not run_command(f"{sys.executable} -m pip install --upgrade pip", "升级pip"):
        print("警告: pip升级失败，继续安装依赖包...")
    
    # 安装依赖包
    packages = [
        "selenium>=4.15.0",
        "requests>=2.31.0", 
        "webdriver-manager>=4.0.0"
    ]
    
    for package in packages:
        if not run_command(f"{sys.executable} -m pip install {package}", f"安装{package}"):
            print(f"✗ 安装{package}失败")
            return False
    
    print("✓ 所有Python依赖包安装完成")
    return True

def check_chrome():
    """检查Chrome浏览器"""
    print("\n=== 检查Chrome浏览器 ===")
    
    system = platform.system().lower()
    
    if system == "windows":
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
        ]
    elif system == "darwin":  # macOS
        chrome_paths = [
            "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
        ]
    else:  # Linux
        chrome_paths = [
            "/usr/bin/google-chrome",
            "/usr/bin/google-chrome-stable",
            "/usr/bin/chromium-browser"
        ]
    
    for path in chrome_paths:
        if os.path.exists(path):
            print(f"✓ 找到Chrome浏览器: {path}")
            return True
    
    print("✗ 未找到Chrome浏览器")
    print("请安装Chrome浏览器:")
    print("  Windows/macOS: https://www.google.com/chrome/")
    print("  Ubuntu/Debian: sudo apt-get install google-chrome-stable")
    print("  CentOS/RHEL: sudo yum install google-chrome-stable")
    return False

def setup_chromedriver():
    """设置ChromeDriver"""
    print("\n=== 设置ChromeDriver ===")
    
    try:
        from webdriver_manager.chrome import ChromeDriverManager
        from selenium import webdriver
        from selenium.webdriver.chrome.service import Service
        
        print("正在下载ChromeDriver...")
        driver_path = ChromeDriverManager().install()
        print(f"✓ ChromeDriver已安装到: {driver_path}")
        
        # 测试ChromeDriver
        print("测试ChromeDriver...")
        service = Service(driver_path)
        options = webdriver.ChromeOptions()
        options.add_argument("--headless")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        
        driver = webdriver.Chrome(service=service, options=options)
        driver.get("https://www.google.com")
        driver.quit()
        
        print("✓ ChromeDriver测试成功")
        return True
        
    except Exception as e:
        print(f"✗ ChromeDriver设置失败: {e}")
        return False

def create_desktop_shortcut():
    """创建桌面快捷方式（Windows）"""
    if platform.system().lower() != "windows":
        return
    
    print("\n=== 创建桌面快捷方式 ===")
    
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        shortcut_path = os.path.join(desktop, "腾讯会议下载器.lnk")
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(shortcut_path)
        shortcut.Targetpath = sys.executable
        shortcut.Arguments = os.path.join(os.getcwd(), "download_gui.py")
        shortcut.WorkingDirectory = os.getcwd()
        shortcut.IconLocation = sys.executable
        shortcut.save()
        
        print(f"✓ 桌面快捷方式已创建: {shortcut_path}")
        
    except ImportError:
        print("跳过创建桌面快捷方式（需要pywin32包）")
    except Exception as e:
        print(f"创建桌面快捷方式失败: {e}")

def test_installation():
    """测试安装"""
    print("\n=== 测试安装 ===")
    
    try:
        from tencent_meeting_downloader import TencentMeetingDownloader
        print("✓ 主模块导入成功")
        
        # 创建下载器实例（不启动浏览器）
        downloader = TencentMeetingDownloader(headless=True)
        print("✓ 下载器初始化成功")
        
        downloader.close()
        print("✓ 安装测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 安装测试失败: {e}")
        return False

def main():
    """主安装流程"""
    print("=== 腾讯会议录制内容下载器 - 安装程序 ===")
    print("此程序将自动安装所需的依赖包和配置环境\n")
    
    # 检查基本环境
    if not check_python_version():
        print("\n安装失败: Python版本不符合要求")
        sys.exit(1)
    
    if not check_pip():
        print("\n安装失败: pip不可用")
        sys.exit(1)
    
    # 安装依赖包
    if not install_requirements():
        print("\n安装失败: 依赖包安装失败")
        sys.exit(1)
    
    # 检查Chrome浏览器
    if not check_chrome():
        print("\n警告: Chrome浏览器未安装，请手动安装后再运行程序")
    
    # 设置ChromeDriver
    if not setup_chromedriver():
        print("\n警告: ChromeDriver设置失败，可能需要手动配置")
    
    # 创建桌面快捷方式
    create_desktop_shortcut()
    
    # 测试安装
    if test_installation():
        print("\n=== 安装完成 ===")
        print("您现在可以使用以下方式运行程序:")
        print("1. 命令行版本: python download_cli.py --help")
        print("2. 图形界面版本: python download_gui.py")
        print("3. 编程接口: python download_example.py")
        print("\n请查看README.md了解详细使用说明")
    else:
        print("\n安装可能存在问题，请检查错误信息并重新安装")
        sys.exit(1)

if __name__ == "__main__":
    main()
