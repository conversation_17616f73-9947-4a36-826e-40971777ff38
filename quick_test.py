#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试脚本 - 验证基本功能
"""

def test_basic_imports():
    """测试基本导入"""
    print("测试基本导入...")
    
    try:
        import selenium
        print(f"✓ Selenium: {selenium.__version__}")
    except ImportError as e:
        print(f"✗ Selenium导入失败: {e}")
        return False
    
    try:
        import requests
        print(f"✓ Requests: {requests.__version__}")
    except ImportError as e:
        print(f"✗ Requests导入失败: {e}")
        return False
    
    try:
        from tencent_meeting_downloader import TencentMeetingDownloader
        print("✓ 主下载器模块导入成功")
    except ImportError as e:
        print(f"✗ 主下载器模块导入失败: {e}")
        return False
    
    try:
        from config import get_config
        config = get_config()
        print(f"✓ 配置模块导入成功，默认下载目录: {config.DEFAULT_DOWNLOAD_DIR}")
    except ImportError as e:
        print(f"✗ 配置模块导入失败: {e}")
        return False
    
    return True

def test_chrome_driver():
    """测试ChromeDriver"""
    print("\n测试ChromeDriver...")
    
    try:
        from webdriver_manager.chrome import ChromeDriverManager
        driver_path = ChromeDriverManager().install()
        print(f"✓ ChromeDriver路径: {driver_path}")
        return True
    except Exception as e:
        print(f"✗ ChromeDriver测试失败: {e}")
        return False

def test_url_validation():
    """测试URL验证"""
    print("\n测试URL验证...")
    
    test_url = "https://meeting.tencent.com/cw/lvm1Z7Gj4f"
    if test_url.startswith('https://meeting.tencent.com/cw/'):
        print(f"✓ URL格式正确: {test_url}")
        return True
    else:
        print(f"✗ URL格式错误: {test_url}")
        return False

def main():
    """主测试函数"""
    print("腾讯会议录制内容下载器 - 快速测试")
    print("=" * 40)
    
    tests = [
        test_basic_imports,
        test_chrome_driver,
        test_url_validation
    ]
    
    passed = 0
    for test in tests:
        if test():
            passed += 1
        else:
            print("测试失败，请检查环境配置")
            return False
    
    print(f"\n✓ 所有测试通过 ({passed}/{len(tests)})")
    print("\n系统已准备就绪！可以使用以下方式运行程序:")
    print("1. 图形界面: python download_gui.py")
    print("2. 命令行: python download_cli.py <URL>")
    print("3. 示例脚本: python download_example.py")
    
    return True

if __name__ == "__main__":
    main()
