#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的登录和下载流程
专门解决扫码登录后无法下载的问题
"""

import time
import os
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from tencent_meeting_downloader import TencentMeetingDownloader
from cookie_manager import CookieManager

def improved_login_and_download():
    """改进的登录和下载流程"""
    print("🚀 改进的腾讯会议登录和下载流程")
    print("=" * 60)
    
    # 获取URL
    print("请输入腾讯会议录制文件URL:")
    url = input("URL: ").strip()
    
    if not url:
        print("使用默认测试URL")
        url = "https://meeting.tencent.com/cw/lvm1Z7Gj4f"
    
    try:
        # 首先检查是否有有效的cookies
        manager = CookieManager()
        has_valid_cookies = manager.is_cookies_valid()

        if has_valid_cookies:
            print("✓ 检测到有效的cookies，将尝试自动登录")
            use_cookies = True
        else:
            print("⚠️ 未检测到有效cookies，需要手动登录")
            use_cookies = False

        # 创建下载器
        downloader = TencentMeetingDownloader(
            headless=False,  # 显示浏览器
            use_cookies=use_cookies  # 根据cookie状态决定
        )
        
        print(f"\n步骤1: 访问录制文件页面")
        print(f"URL: {url}")
        downloader.driver.get(url)
        
        print("\n步骤2: 等待页面加载...")
        time.sleep(3)
        
        # 检查页面状态
        page_title = downloader.driver.title
        print(f"页面标题: {page_title}")
        
        # 检查是否需要登录
        page_source = downloader.driver.page_source
        needs_login = any(indicator in page_source.lower() for indicator in
                         ["登录", "login", "扫码", "qr", "二维码"])

        if needs_login and not use_cookies:
            print("\n步骤3: 检测到需要登录")
            print("请在浏览器中完成以下操作:")
            print("1. 扫码登录您的腾讯会议账号")
            print("2. 登录成功后，确保可以看到录制文件内容")
            print("3. 确认页面显示正常后，按回车继续...")

            input("登录完成后按回车继续...")
        elif needs_login and use_cookies:
            print("\n步骤3: 检测到需要登录，但已有cookies")
            print("⚠️ Cookies可能已过期，需要重新登录")
            print("请在浏览器中扫码登录，然后按回车继续...")
            input("登录完成后按回车继续...")
        else:
            print("\n步骤3: ✓ 已登录或无需登录")
        
        print("\n步骤4: 验证登录状态和页面内容...")
        time.sleep(2)
        
        # 重新获取页面源码
        page_source = downloader.driver.page_source
        
        # 检查是否有录制内容
        content_indicators = ["录制", "视频", "音频", "转录", "摘要", "另存为", "下载"]
        content_found = False
        for indicator in content_indicators:
            if indicator in page_source:
                print(f"✓ 发现内容: {indicator}")
                content_found = True
                break
        
        if not content_found:
            print("⚠️ 未发现录制内容，可能存在以下问题:")
            print("1. 录制文件不存在或已过期")
            print("2. 没有访问权限")
            print("3. 页面加载不完整")
            
            choice = input("是否继续尝试下载? (y/n): ").strip().lower()
            if choice != 'y':
                downloader.close()
                return
        
        print("\n步骤5: 保存登录状态...")
        # 保存当前的cookies
        if downloader.cookie_manager.save_cookies(downloader.driver):
            print("✓ 登录状态已保存")
        else:
            print("⚠️ 登录状态保存失败")
        
        print("\n步骤6: 查找下载按钮...")
        
        # 尝试多种方式查找下载按钮
        download_button = None
        button_selectors = [
            (By.CLASS_NAME, "saveas-btn"),
            (By.XPATH, "//button[contains(text(), '另存为')]"),
            (By.XPATH, "//button[contains(text(), '下载')]"),
            (By.XPATH, "//div[contains(@class, 'saveas')]"),
            (By.XPATH, "//a[contains(text(), '下载')]"),
        ]
        
        for selector_type, selector_value in button_selectors:
            try:
                download_button = downloader.driver.find_element(selector_type, selector_value)
                print(f"✓ 找到下载按钮: {selector_value}")
                break
            except NoSuchElementException:
                continue
        
        if not download_button:
            print("✗ 未找到下载按钮")
            print("可能的原因:")
            print("1. 页面结构发生变化")
            print("2. 需要特殊权限")
            print("3. 录制文件不支持下载")
            
            # 让用户手动查找
            print("\n请在浏览器中手动查找下载按钮:")
            print("1. 查看页面是否有'另存为'、'下载'等按钮")
            print("2. 尝试右键点击查看选项")
            print("3. 检查页面菜单栏")
            
            choice = input("找到下载按钮了吗? (y/n): ").strip().lower()
            if choice != 'y':
                downloader.close()
                return
        
        print("\n步骤7: 尝试下载...")
        
        if download_button and download_button.is_displayed() and download_button.is_enabled():
            try:
                # 点击下载按钮
                download_button.click()
                print("✓ 已点击下载按钮")
                
                # 等待下载菜单出现
                time.sleep(3)

                # 尝试多种下载选项识别方式
                download_success = False

                # 方式1: 查找视频内容选项
                video_selectors = [
                    "//div[contains(text(), '视频内容')]",
                    "//span[contains(text(), '视频内容')]",
                    "//li[contains(text(), '视频内容')]",
                    "//div[contains(text(), '视频')]",
                    "//span[contains(text(), '视频')]"
                ]

                for selector in video_selectors:
                    try:
                        video_option = downloader.driver.find_element(By.XPATH, selector)
                        if video_option.is_displayed():
                            video_option.click()
                            print(f"✓ 已选择视频内容下载 (选择器: {selector})")
                            download_success = True
                            break
                    except NoSuchElementException:
                        continue

                # 方式2: 如果没找到视频选项，尝试其他下载选项
                if not download_success:
                    print("⚠️ 未找到视频下载选项，尝试其他选项...")

                    general_selectors = [
                        "//div[contains(@class, 'download-option')]",
                        "//li[contains(@class, 'download')]",
                        "//div[contains(@class, 'dropdown-item')]",
                        "//ul[@class='dropdown-menu']//li",
                        "//div[contains(@class, 'menu-item')]"
                    ]

                    for selector in general_selectors:
                        try:
                            download_options = downloader.driver.find_elements(By.XPATH, selector)
                            if download_options:
                                for option in download_options:
                                    if option.is_displayed() and option.text:
                                        option.click()
                                        print(f"✓ 已选择下载选项: {option.text}")
                                        download_success = True
                                        break
                                if download_success:
                                    break
                        except Exception as e:
                            continue

                if download_success:
                    # 等待下载开始
                    time.sleep(5)
                    print("✓ 下载已开始，请检查下载目录")
                else:
                    print("✗ 未找到任何下载选项")
                    print("请手动在浏览器中选择下载选项")
                
            except Exception as e:
                print(f"✗ 点击下载按钮失败: {e}")
        
        print("\n步骤8: 检查下载结果...")
        
        # 检查下载目录
        download_dir = downloader.download_dir
        print(f"下载目录: {download_dir}")
        
        if os.path.exists(download_dir):
            files = os.listdir(download_dir)
            new_files = [f for f in files if not f.endswith('.py') and not f.endswith('.json')]
            
            if new_files:
                print(f"✓ 发现 {len(new_files)} 个新文件:")
                for file in new_files[:5]:  # 只显示前5个
                    print(f"  - {file}")
            else:
                print("⚠️ 下载目录中未发现新文件")
                print("可能的原因:")
                print("1. 下载仍在进行中")
                print("2. 文件下载到了其他位置")
                print("3. 下载失败")
        
        print("\n步骤9: 保持浏览器打开以便进一步操作...")
        print("您可以:")
        print("1. 手动尝试其他下载选项")
        print("2. 检查浏览器的下载管理器")
        print("3. 查看是否有弹出的下载对话框")
        
        input("操作完成后按回车关闭浏览器...")
        
        downloader.close()
        
        print("\n✅ 流程完成!")
        print("如果下载成功，文件应该在以下位置:")
        print(f"📁 {download_dir}")
        
    except Exception as e:
        print(f"✗ 流程执行失败: {e}")
        print("建议:")
        print("1. 检查网络连接")
        print("2. 更新Chrome浏览器")
        print("3. 尝试使用诊断工具: python diagnose_login_issue.py")

def quick_test_with_saved_cookies():
    """使用已保存的cookies快速测试"""
    print("\n🔄 使用已保存的Cookies快速测试")
    print("=" * 50)
    
    manager = CookieManager()
    if not manager.is_cookies_valid():
        print("✗ 没有有效的cookies，请先完成登录流程")
        return False
    
    print("✓ 发现有效cookies，开始测试...")
    
    url = input("请输入测试URL (回车使用默认): ").strip()
    if not url:
        url = "https://meeting.tencent.com/cw/lvm1Z7Gj4f"
    
    try:
        downloader = TencentMeetingDownloader(
            headless=False,
            use_cookies=True
        )
        
        success = downloader.open_meeting_page(url)
        
        if success:
            print("✅ 页面访问成功！Cookies有效")
            
            # 尝试下载
            choice = input("是否尝试下载? (y/n): ").strip().lower()
            if choice == 'y':
                downloader.download_all_content(url, ['video'])
        else:
            print("❌ 页面访问失败，可能需要重新登录")
        
        downloader.close()
        return success
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def main():
    """主菜单"""
    print("🔧 腾讯会议下载问题解决工具")
    print("=" * 60)
    
    print("请选择操作:")
    print("1. 完整登录和下载流程（推荐）")
    print("2. 使用已保存cookies快速测试")
    print("3. 运行诊断工具")
    print("4. 退出")
    
    choice = input("\n请选择 (1-4): ").strip()
    
    if choice == '1':
        improved_login_and_download()
    elif choice == '2':
        quick_test_with_saved_cookies()
    elif choice == '3':
        os.system("python diagnose_login_issue.py")
    elif choice == '4':
        print("退出程序")
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
