#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
腾讯会议下载器配置文件
"""

import os

# 基本配置
class Config:
    # 默认下载目录
    DEFAULT_DOWNLOAD_DIR = os.getcwd()  # 当前目录
    
    # 浏览器配置
    HEADLESS_MODE = False  # 是否使用无头模式
    BROWSER_TIMEOUT = 30   # 浏览器操作超时时间（秒）
    
    # 下载配置
    DOWNLOAD_TIMEOUT = 300  # 下载超时时间（秒）
    RETRY_ATTEMPTS = 3      # 重试次数
    RETRY_DELAY = 2         # 重试间隔（秒）
    
    # 文件命名配置
    FILE_NAME_PREFIX = ""   # 文件名前缀
    ADD_TIMESTAMP = True    # 是否在文件名中添加时间戳
    
    # 默认下载内容类型
    DEFAULT_CONTENT_TYPES = [
        'video',                # 视频内容
        'audio',                # 纯音频文件
        'transcript_original',  # 原文版转写
        'summary_topic',        # 主题摘要
    ]
    
    # 所有可用的内容类型
    ALL_CONTENT_TYPES = [
        'video',                # 视频内容
        'audio',                # 纯音频文件
        'transcript_original',  # 原文版转写文本
        'transcript_optimized', # 智能优化版转写文本
        'summary_topic',        # 主题摘要
        'summary_chapter',      # 分章节小结
        'summary_speaker',      # 发言人观点
        'summary_deepseek'      # DeepSeek摘要
    ]
    
    # 内容类型中文名称映射
    CONTENT_TYPE_NAMES = {
        'video': '视频内容',
        'audio': '纯音频文件',
        'transcript_original': '原文版转写文本',
        'transcript_optimized': '智能优化版转写文本',
        'summary_topic': '主题摘要',
        'summary_chapter': '分章节小结',
        'summary_speaker': '发言人观点',
        'summary_deepseek': 'DeepSeek摘要'
    }
    
    # Chrome浏览器选项
    CHROME_OPTIONS = [
        "--no-sandbox",
        "--disable-dev-shm-usage",
        "--disable-gpu",
        "--window-size=1920,1080",
        "--disable-blink-features=AutomationControlled",
        "--disable-extensions",
        "--no-first-run",
        "--disable-default-apps",
        "--disable-infobars"
    ]

# 用户自定义配置（可以在这里修改）
class UserConfig(Config):
    # 在这里覆盖默认配置
    
    # 例如：修改默认下载目录
    # DEFAULT_DOWNLOAD_DIR = "D:/Downloads/TencentMeeting"
    
    # 例如：启用无头模式
    # HEADLESS_MODE = True
    
    # 例如：修改默认下载类型
    # DEFAULT_CONTENT_TYPES = ['video', 'audio']
    
    pass

# 获取配置的便捷函数
def get_config():
    """获取配置对象"""
    return UserConfig()

def get_download_dir():
    """获取下载目录"""
    config = get_config()
    return config.DEFAULT_DOWNLOAD_DIR

def get_content_types():
    """获取默认内容类型"""
    config = get_config()
    return config.DEFAULT_CONTENT_TYPES

def get_content_type_name(content_type):
    """获取内容类型的中文名称"""
    config = get_config()
    return config.CONTENT_TYPE_NAMES.get(content_type, content_type)
