#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的cookie获取和下载流程
"""

import os
import time
from tencent_meeting_downloader import TencentMeetingDownloader
from cookie_manager import <PERSON><PERSON><PERSON>ana<PERSON>

def check_cookie_status():
    """检查cookie状态"""
    print("检查Cookie状态")
    print("=" * 30)
    
    manager = CookieManager()
    info = manager.get_cookie_info()
    
    if info:
        print(f"✓ 找到cookie文件")
        print(f"  保存时间: {info['saved_time']}")
        print(f"  存在天数: {info['age_days']} 天")
        print(f"  Cookie数量: {info['cookie_count']}")
        print(f"  是否有效: {'是' if info['is_valid'] else '否'}")
        return info['is_valid']
    else:
        print("✗ 未找到cookie文件")
        return False

def manual_login_and_save_cookies():
    """手动登录并保存cookies"""
    print("\n手动登录模式")
    print("=" * 30)
    
    try:
        downloader = TencentMeetingDownloader(
            headless=False,  # 显示浏览器
            use_cookies=False  # 不使用现有cookies
        )
        
        print("1. 浏览器已打开")
        print("2. 请导航到腾讯会议页面并登录")
        print("3. 登录完成后访问您要下载的录制文件页面")
        print("4. 确认页面可以正常访问后，按回车继续...")
        
        input("按回车继续...")
        
        # 保存当前页面的cookies
        if downloader.cookie_manager.save_cookies(downloader.driver):
            print("✓ Cookies保存成功")
            downloader.close()
            return True
        else:
            print("✗ Cookies保存失败")
            downloader.close()
            return False
            
    except Exception as e:
        print(f"✗ 登录过程失败: {e}")
        return False

def test_download_with_cookies(url):
    """使用cookies测试下载"""
    print(f"\n使用Cookies测试下载")
    print("=" * 30)
    print(f"测试URL: {url}")
    
    try:
        downloader = TencentMeetingDownloader(
            headless=False,  # 显示浏览器以便观察
            use_cookies=True  # 使用保存的cookies
        )
        
        print("正在尝试访问页面...")
        success = downloader.open_meeting_page(url)
        
        if success:
            print("✓ 页面访问成功！")
            print("✓ Cookies有效，可以进行下载")
            
            # 询问是否开始下载
            choice = input("是否开始下载? (y/n): ").strip().lower()
            if choice == 'y':
                print("开始下载...")
                try:
                    # 下载所有可用内容
                    downloader.download_all_content(url, ['video', 'audio'])
                    print("✓ 下载完成")
                except Exception as e:
                    print(f"✗ 下载过程出错: {e}")
            else:
                print("跳过下载")
                
        else:
            print("✗ 页面访问失败")
            print("可能的原因:")
            print("1. Cookies已过期")
            print("2. 需要重新登录")
            print("3. 该录制文件需要特殊权限")
        
        # 保持浏览器打开一段时间以便观察
        print("浏览器将保持打开10秒...")
        time.sleep(10)
        
        downloader.close()
        return success
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def main():
    """主流程"""
    print("腾讯会议录制文件下载器 - Cookie版")
    print("=" * 50)
    
    # 1. 检查cookie状态
    has_valid_cookies = check_cookie_status()
    
    # 2. 如果没有有效cookies，进行登录
    if not has_valid_cookies:
        print("\n需要获取有效的cookies")
        choice = input("是否现在进行登录? (y/n): ").strip().lower()
        
        if choice == 'y':
            if not manual_login_and_save_cookies():
                print("登录失败，程序退出")
                return
        else:
            print("请先获取有效的cookies后再运行此程序")
            print("可以使用以下方式:")
            print("1. python mcp_cookie_extractor.py")
            print("2. 重新运行此程序并选择登录")
            return
    
    # 3. 获取要下载的URL
    print("\n请输入要下载的腾讯会议录制文件URL:")
    url = input("URL: ").strip()
    
    if not url:
        print("未输入URL，使用默认测试URL")
        url = "https://meeting.tencent.com/cw/lvm1Z7Gj4f"
    
    # 4. 使用cookies进行下载测试
    success = test_download_with_cookies(url)
    
    # 5. 总结
    print("\n" + "=" * 50)
    print("流程总结:")
    if success:
        print("✓ Cookie功能正常")
        print("✓ 页面访问成功")
        print("✓ 可以进行正常下载")
        print("\n后续使用:")
        print("1. 运行 python download_gui.py 使用图形界面")
        print("2. 运行 python download_cli.py <URL> 使用命令行")
        print("3. 下载器会自动使用保存的cookies")
    else:
        print("✗ 存在问题，需要检查:")
        print("1. 网络连接是否正常")
        print("2. 是否有访问该录制文件的权限")
        print("3. Cookies是否已过期")
        print("4. 录制文件URL是否正确")

if __name__ == "__main__":
    main()
