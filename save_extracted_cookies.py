#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
保存通过MCP提取的cookies
"""

import json
import os
from datetime import datetime
from cookie_manager import <PERSON>ieManager

def save_mcp_cookies():
    """
    保存从MCP获取的cookie数据
    这里我们手动输入从页面获取的cookie信息
    """
    print("保存MCP提取的cookies")
    print("=" * 40)
    
    # 基于截图显示的信息，我们知道有8个cookies
    # 这里创建一个示例cookie结构
    cookie_data = {
        "cookies": [
            {
                "name": "session_id",
                "value": "example_session_value",
                "domain": "meeting.tencent.com",
                "path": "/",
                "secure": True,
                "httpOnly": False
            },
            # 注意：实际的cookie值需要从真实的登录会话中获取
            # 这里只是示例结构
        ],
        "domain": "meeting.tencent.com",
        "url": "https://meeting.tencent.com/cw/lvm1Z7Gj4f",
        "timestamp": datetime.now().isoformat(),
        "cookieCount": 8,
        "pageTitle": "录制文件",
        "source": "mcp_extraction"
    }
    
    try:
        manager = CookieManager()
        
        # 保存cookies
        save_data = {
            'cookies': cookie_data['cookies'],
            'saved_time': cookie_data['timestamp'],
            'domain': cookie_data['domain'],
            'source': 'mcp_manual',
            'url': cookie_data['url'],
            'page_title': cookie_data['pageTitle']
        }
        
        with open(manager.cookie_path, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, ensure_ascii=False, indent=2)
        
        print(f"✓ 成功保存cookies到: {manager.cookie_path}")
        print(f"✓ Cookie数量: {len(cookie_data['cookies'])}")
        print(f"✓ 域名: {cookie_data['domain']}")
        print(f"✓ 页面标题: {cookie_data['pageTitle']}")
        
        return True
        
    except Exception as e:
        print(f"✗ 保存失败: {e}")
        return False

def create_login_instructions():
    """
    创建登录说明
    """
    print("\n" + "=" * 50)
    print("Cookie获取和使用说明")
    print("=" * 50)
    
    print("\n📋 获取有效cookies的步骤:")
    print("1. 在浏览器中访问腾讯会议页面")
    print("2. 登录您的腾讯会议账号")
    print("3. 访问需要下载的录制文件页面")
    print("4. 按F12打开开发者工具")
    print("5. 在Console中运行cookie提取脚本")
    print("6. 复制获取的cookie数据")
    print("7. 使用mcp_cookie_extractor.py保存cookies")
    
    print("\n🔧 使用cookies进行下载:")
    print("1. 确保cookies已保存")
    print("2. 运行下载器时会自动加载cookies")
    print("3. 如果cookies有效，将能够访问需要权限的页面")
    print("4. 下载器会自动下载录制文件")
    
    print("\n⚠️ 注意事项:")
    print("1. Cookies有时效性，通常7天内有效")
    print("2. 如果下载失败，可能需要重新获取cookies")
    print("3. 不同的录制文件可能需要不同的权限")
    print("4. 确保您有访问该录制文件的权限")

def test_cookie_functionality():
    """
    测试cookie功能
    """
    print("\n测试cookie功能")
    print("=" * 30)
    
    try:
        manager = CookieManager()
        info = manager.get_cookie_info()
        
        if info:
            print(f"✓ 找到cookie文件")
            print(f"  保存时间: {info['saved_time']}")
            print(f"  存在天数: {info['age_days']} 天")
            print(f"  Cookie数量: {info['cookie_count']}")
            print(f"  是否有效: {'是' if info['is_valid'] else '否'}")
            
            if info['is_valid']:
                print("\n✓ Cookies有效，可以尝试下载")
                print("运行命令: python download_gui.py")
                print("或者: python download_cli.py <URL>")
            else:
                print("\n⚠️ Cookies已过期，需要重新获取")
                
        else:
            print("✗ 未找到cookie文件")
            print("请先使用mcp_cookie_extractor.py获取cookies")
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")

def main():
    """主函数"""
    print("MCP Cookie保存工具")
    print("=" * 50)
    
    # 注意：这里只是演示cookie保存的结构
    # 实际使用时需要从真实的登录会话中获取cookie值
    print("⚠️ 注意：此脚本仅演示cookie保存结构")
    print("实际使用请通过以下方式获取真实cookies:")
    print("1. 使用 python mcp_cookie_extractor.py")
    print("2. 或手动从浏览器开发者工具获取")
    
    choice = input("\n是否继续演示保存功能? (y/n): ").strip().lower()
    
    if choice == 'y':
        save_mcp_cookies()
        test_cookie_functionality()
    
    create_login_instructions()

if __name__ == "__main__":
    main()
