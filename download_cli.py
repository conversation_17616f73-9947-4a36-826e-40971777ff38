#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
腾讯会议录制内容下载器 - 命令行版本
支持通过命令行参数指定URL和下载选项
"""

import argparse
import sys
import os
from tencent_meeting_downloader import TencentMeetingDownloader
from config import get_config, get_content_type_name

def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="腾讯会议录制内容下载器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python download_cli.py https://meeting.tencent.com/cw/xxx
  python download_cli.py https://meeting.tencent.com/cw/xxx --types video audio
  python download_cli.py https://meeting.tencent.com/cw/xxx --dir ./downloads --headless
  python download_cli.py https://meeting.tencent.com/cw/xxx --all

支持的内容类型:
  video                 视频内容
  audio                 纯音频文件
  transcript_original   原文版转写文本
  transcript_optimized  智能优化版转写文本
  summary_topic         主题摘要
  summary_chapter       分章节小结
  summary_speaker       发言人观点
  summary_deepseek      DeepSeek摘要
        """
    )
    
    parser.add_argument(
        'url',
        help='腾讯会议录制页面URL'
    )
    
    parser.add_argument(
        '--types', '-t',
        nargs='+',
        choices=[
            'video', 'audio', 'transcript_original', 'transcript_optimized',
            'summary_topic', 'summary_chapter', 'summary_speaker', 'summary_deepseek'
        ],
        help='指定要下载的内容类型（可多选）'
    )
    
    parser.add_argument(
        '--all', '-a',
        action='store_true',
        help='下载所有类型的内容'
    )
    
    parser.add_argument(
        '--dir', '-d',
        default=None,
        help='指定下载目录'
    )
    
    parser.add_argument(
        '--headless',
        action='store_true',
        help='使用无头模式运行（后台运行，无界面）'
    )
    
    parser.add_argument(
        '--timeout',
        type=int,
        default=300,
        help='下载超时时间（秒），默认300秒'
    )
    
    parser.add_argument(
        '--list-types',
        action='store_true',
        help='列出所有支持的内容类型'
    )
    
    return parser.parse_args()

def list_content_types():
    """列出所有支持的内容类型"""
    config = get_config()
    print("支持的内容类型:")
    print("-" * 50)
    for content_type in config.ALL_CONTENT_TYPES:
        name = get_content_type_name(content_type)
        print(f"  {content_type:<20} {name}")
    print("-" * 50)

def validate_url(url):
    """验证URL格式"""
    if not url.startswith('https://meeting.tencent.com/'):
        print("错误: URL必须是腾讯会议的录制页面链接")
        print("正确格式: https://meeting.tencent.com/cw/xxx")
        return False
    return True

def main():
    """主函数"""
    args = parse_arguments()
    
    # 如果只是列出类型，直接返回
    if args.list_types:
        list_content_types()
        return
    
    # 验证URL
    if not validate_url(args.url):
        sys.exit(1)
    
    # 确定要下载的内容类型
    config = get_config()
    
    if args.all:
        content_types = config.ALL_CONTENT_TYPES
    elif args.types:
        content_types = args.types
    else:
        content_types = config.DEFAULT_CONTENT_TYPES
    
    # 确定下载目录
    download_dir = args.dir or config.DEFAULT_DOWNLOAD_DIR
    
    # 显示配置信息
    print("=== 腾讯会议录制内容下载器 ===")
    print(f"URL: {args.url}")
    print(f"下载目录: {download_dir}")
    print(f"无头模式: {'是' if args.headless else '否'}")
    print(f"下载超时: {args.timeout}秒")
    print()
    print("将下载以下内容:")
    for content_type in content_types:
        name = get_content_type_name(content_type)
        print(f"  ✓ {name}")
    print()
    
    # 确认下载
    try:
        confirm = input("确认开始下载? (y/N): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            print("取消下载")
            return
    except KeyboardInterrupt:
        print("\n取消下载")
        return
    
    # 创建下载器
    downloader = TencentMeetingDownloader(
        headless=args.headless,
        download_dir=download_dir
    )
    
    try:
        print("\n开始下载...")
        
        # 执行下载
        downloader.download_all_content(args.url, content_types)
        
        print("\n=== 下载完成 ===")
        print(f"文件已保存到: {download_dir}")
        
        # 列出下载的文件
        if os.path.exists(download_dir):
            files = os.listdir(download_dir)
            if files:
                print("\n下载的文件:")
                for file in sorted(files):
                    file_path = os.path.join(download_dir, file)
                    if os.path.isfile(file_path):
                        size = os.path.getsize(file_path)
                        size_mb = size / (1024 * 1024)
                        print(f"  {file} ({size_mb:.1f} MB)")
        
    except KeyboardInterrupt:
        print("\n\n用户取消下载")
    except Exception as e:
        print(f"\n下载过程中出现错误: {e}")
        print("\n故障排除建议:")
        print("1. 检查网络连接是否正常")
        print("2. 确认URL是否正确且有访问权限")
        print("3. 检查Chrome浏览器是否已安装")
        print("4. 确认下载目录是否有写入权限")
        print("5. 尝试使用 --headless 参数")
        sys.exit(1)
    finally:
        downloader.close()

if __name__ == "__main__":
    main()
