#!/bin/bash

echo "腾讯会议录制内容下载器 - GUI版本"
echo "================================"

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "错误: 未找到Python，请先安装Python 3.7或更高版本"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo "使用Python命令: $PYTHON_CMD"

# 检查Python版本
PYTHON_VERSION=$($PYTHON_CMD -c "import sys; print(f'{sys.version_info.major}.{sys.version_info.minor}')")
echo "Python版本: $PYTHON_VERSION"

# 检查是否已安装依赖
$PYTHON_CMD -c "import selenium, requests" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "正在安装依赖包..."
    $PYTHON_CMD install.py
    if [ $? -ne 0 ]; then
        echo "依赖安装失败，请手动运行: pip install -r requirements.txt"
        exit 1
    fi
fi

# 运行GUI程序
echo "启动图形界面..."
$PYTHON_CMD download_gui.py

if [ $? -ne 0 ]; then
    echo "程序运行出错"
    read -p "按Enter键退出..."
fi
