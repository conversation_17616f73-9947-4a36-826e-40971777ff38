@echo off
chcp 65001 >nul
echo 腾讯会议录制内容下载器 - GUI版本
echo ================================

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查是否已安装依赖
python -c "import selenium, requests" >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    python install.py
    if errorlevel 1 (
        echo 依赖安装失败，请手动运行: pip install -r requirements.txt
        pause
        exit /b 1
    )
)

REM 运行GUI程序
echo 启动图形界面...
python download_gui.py

if errorlevel 1 (
    echo 程序运行出错
    pause
)
