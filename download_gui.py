#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
腾讯会议录制内容下载器 - GUI版本
提供图形界面操作
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
import sys
from tencent_meeting_downloader import TencentMeetingDownloader
from config import get_config, get_content_type_name

class DownloadGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("腾讯会议录制内容下载器")
        self.root.geometry("800x600")
        
        self.config = get_config()
        self.downloader = None
        self.download_thread = None
        
        self.setup_ui()
        
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # URL输入
        url_frame = ttk.LabelFrame(main_frame, text="会议录制URL", padding="5")
        url_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.url_var = tk.StringVar(value="https://meeting.tencent.com/cw/")
        url_entry = ttk.Entry(url_frame, textvariable=self.url_var, width=80)
        url_entry.grid(row=0, column=0, sticky=(tk.W, tk.E))
        url_frame.columnconfigure(0, weight=1)
        
        # 下载目录选择
        dir_frame = ttk.LabelFrame(main_frame, text="下载目录", padding="5")
        dir_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.dir_var = tk.StringVar(value=os.getcwd())  # 使用当前目录
        dir_entry = ttk.Entry(dir_frame, textvariable=self.dir_var, width=60)
        dir_entry.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        dir_button = ttk.Button(dir_frame, text="浏览", command=self.browse_directory)
        dir_button.grid(row=0, column=1, padx=(5, 0))
        dir_frame.columnconfigure(0, weight=1)
        
        # 内容类型选择
        content_frame = ttk.LabelFrame(main_frame, text="下载内容选择", padding="5")
        content_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.content_vars = {}
        row = 0
        col = 0
        for content_type in self.config.ALL_CONTENT_TYPES:
            var = tk.BooleanVar()
            if content_type in self.config.DEFAULT_CONTENT_TYPES:
                var.set(True)
            
            self.content_vars[content_type] = var
            name = get_content_type_name(content_type)
            
            cb = ttk.Checkbutton(content_frame, text=name, variable=var)
            cb.grid(row=row, column=col, sticky=tk.W, padx=(0, 20), pady=2)
            
            col += 1
            if col >= 2:
                col = 0
                row += 1
        
        # 选择按钮
        button_frame = ttk.Frame(content_frame)
        button_frame.grid(row=row+1, column=0, columnspan=2, pady=(10, 0))
        
        ttk.Button(button_frame, text="全选", command=self.select_all).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="全不选", command=self.select_none).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(button_frame, text="默认选择", command=self.select_default).pack(side=tk.LEFT)
        
        # 选项设置
        options_frame = ttk.LabelFrame(main_frame, text="选项", padding="5")
        options_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.headless_var = tk.BooleanVar(value=self.config.HEADLESS_MODE)
        ttk.Checkbutton(options_frame, text="无头模式运行（后台运行，无浏览器界面）", 
                       variable=self.headless_var).grid(row=0, column=0, sticky=tk.W)
        
        # 控制按钮
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=4, column=0, columnspan=2, pady=(0, 10))
        
        self.download_button = ttk.Button(control_frame, text="开始下载", command=self.start_download)
        self.download_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_button = ttk.Button(control_frame, text="停止下载", command=self.stop_download, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT)
        
        # 进度显示
        progress_frame = ttk.LabelFrame(main_frame, text="下载进度", padding="5")
        progress_frame.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        
        self.progress_var = tk.StringVar(value="准备就绪")
        progress_label = ttk.Label(progress_frame, textvariable=self.progress_var)
        progress_label.grid(row=0, column=0, sticky=tk.W)
        
        self.progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.progress_bar.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(5, 0))
        progress_frame.columnconfigure(0, weight=1)
        
        # 日志显示
        log_frame = ttk.LabelFrame(main_frame, text="日志", padding="5")
        log_frame.grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=10, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 配置网格权重
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(6, weight=1)
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
    
    def browse_directory(self):
        """浏览选择下载目录"""
        directory = filedialog.askdirectory(initialdir=self.dir_var.get())
        if directory:
            self.dir_var.set(directory)
    
    def select_all(self):
        """全选所有内容类型"""
        for var in self.content_vars.values():
            var.set(True)
    
    def select_none(self):
        """取消选择所有内容类型"""
        for var in self.content_vars.values():
            var.set(False)
    
    def select_default(self):
        """选择默认内容类型"""
        for content_type, var in self.content_vars.items():
            var.set(content_type in self.config.DEFAULT_CONTENT_TYPES)
    
    def log_message(self, message):
        """添加日志消息"""
        self.log_text.insert(tk.END, f"{message}\n")
        self.log_text.see(tk.END)
        self.root.update_idletasks()
    
    def validate_inputs(self):
        """验证输入"""
        url = self.url_var.get().strip()
        if not url:
            messagebox.showerror("错误", "请输入会议录制URL")
            return False
        
        if not url.startswith('https://meeting.tencent.com/'):
            messagebox.showerror("错误", "URL必须是腾讯会议的录制页面链接")
            return False
        
        download_dir = self.dir_var.get().strip()
        if not download_dir:
            messagebox.showerror("错误", "请选择下载目录")
            return False
        
        # 检查是否选择了至少一种内容类型
        selected_types = [ct for ct, var in self.content_vars.items() if var.get()]
        if not selected_types:
            messagebox.showerror("错误", "请至少选择一种要下载的内容类型")
            return False
        
        return True
    
    def start_download(self):
        """开始下载"""
        if not self.validate_inputs():
            return
        
        # 获取选中的内容类型
        selected_types = [ct for ct, var in self.content_vars.items() if var.get()]
        
        # 更新UI状态
        self.download_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.progress_bar.start()
        self.progress_var.set("正在初始化...")
        
        # 清空日志
        self.log_text.delete(1.0, tk.END)
        
        # 在新线程中执行下载
        self.download_thread = threading.Thread(
            target=self.download_worker,
            args=(self.url_var.get(), selected_types, self.dir_var.get(), self.headless_var.get())
        )
        self.download_thread.daemon = True
        self.download_thread.start()
    
    def download_worker(self, url, content_types, download_dir, headless):
        """下载工作线程"""
        try:
            self.log_message("=== 开始下载 ===")
            self.log_message(f"URL: {url}")
            self.log_message(f"下载目录: {download_dir}")
            self.log_message(f"内容类型: {[get_content_type_name(ct) for ct in content_types]}")
            
            # 创建下载器
            self.downloader = TencentMeetingDownloader(
                headless=headless,
                download_dir=download_dir
            )
            
            # 执行下载
            self.progress_var.set("正在下载...")
            self.downloader.download_all_content(url, content_types)
            
            self.log_message("=== 下载完成 ===")
            self.progress_var.set("下载完成")
            
            # 显示完成消息
            self.root.after(0, lambda: messagebox.showinfo("完成", f"下载完成！\n文件已保存到: {download_dir}"))
            
        except Exception as e:
            self.log_message(f"下载失败: {e}")
            self.progress_var.set("下载失败")
            self.root.after(0, lambda: messagebox.showerror("错误", f"下载失败: {e}"))
        
        finally:
            # 恢复UI状态
            self.root.after(0, self.download_finished)
    
    def stop_download(self):
        """停止下载"""
        if self.downloader:
            self.downloader.close()
        
        self.log_message("用户取消下载")
        self.progress_var.set("已取消")
        self.download_finished()
    
    def download_finished(self):
        """下载完成后的UI更新"""
        self.download_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.progress_bar.stop()
        
        if self.downloader:
            self.downloader.close()
            self.downloader = None

def main():
    """主函数"""
    root = tk.Tk()
    app = DownloadGUI(root)
    
    # 设置窗口关闭事件
    def on_closing():
        if app.downloader:
            app.downloader.close()
        root.destroy()
    
    root.protocol("WM_DELETE_WINDOW", on_closing)
    root.mainloop()

if __name__ == "__main__":
    main()
