#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证当前目录下载功能
"""

import os
import tempfile

def test_downloader_current_dir():
    """测试下载器使用当前目录"""
    print("测试下载器当前目录功能")
    print("=" * 30)
    
    try:
        from tencent_meeting_downloader import TencentMeetingDownloader
        
        # 获取当前目录
        current_dir = os.getcwd()
        print(f"当前工作目录: {current_dir}")
        
        # 创建下载器（不指定目录）
        downloader = TencentMeetingDownloader(
            headless=True,
            download_dir=None
        )
        
        print(f"下载器目录: {downloader.download_dir}")
        
        # 检查是否使用当前目录
        if downloader.download_dir == current_dir:
            print("✓ 成功：下载器使用当前目录")
            result = True
        else:
            print("✗ 失败：下载器未使用当前目录")
            result = False
        
        downloader.close()
        return result
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def test_gui_current_dir():
    """测试GUI使用当前目录"""
    print("\n测试GUI当前目录功能")
    print("=" * 30)
    
    try:
        import tkinter as tk
        import os
        
        # 模拟GUI初始化
        current_dir = os.getcwd()
        gui_default = current_dir  # GUI现在使用os.getcwd()
        
        print(f"当前工作目录: {current_dir}")
        print(f"GUI默认目录: {gui_default}")
        
        if gui_default == current_dir:
            print("✓ 成功：GUI使用当前目录")
            return True
        else:
            print("✗ 失败：GUI未使用当前目录")
            return False
            
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("当前目录下载功能验证")
    print("=" * 40)
    
    tests = [
        test_downloader_current_dir,
        test_gui_current_dir,
    ]
    
    passed = 0
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 40)
    print(f"测试结果: {passed}/{len(tests)} 通过")
    
    if passed == len(tests):
        print("\n🎉 验证成功！")
        print("下载器已正确配置为使用当前目录。")
        print("\n使用说明:")
        print("1. 在任意目录运行程序")
        print("2. 文件将下载到运行命令的目录")
        print("3. 例如：")
        print("   cd D:\\MyDownloads")
        print("   python D:\\path\\to\\download_gui.py")
        print("   # 文件将下载到 D:\\MyDownloads")
        return True
    else:
        print("\n⚠️ 验证失败，请检查配置。")
        return False

if __name__ == "__main__":
    main()
