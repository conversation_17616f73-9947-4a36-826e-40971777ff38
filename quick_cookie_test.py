#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试cookie是否有效
"""

import time
from tencent_meeting_downloader import TencentMeetingDownloader
from cookie_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>

def quick_test():
    """快速测试cookie功能"""
    print("🚀 快速Cookie测试")
    print("=" * 40)
    
    # 检查cookie状态
    manager = CookieManager()
    info = manager.get_cookie_info()
    
    if not info:
        print("❌ 未找到cookie文件")
        print("请先运行: python improved_login_download.py")
        return
    
    print(f"✓ Cookie文件存在")
    print(f"  数量: {info['cookie_count']}")
    print(f"  有效: {'是' if info['is_valid'] else '否'}")
    
    if not info['is_valid']:
        print("❌ Cookies已过期")
        return
    
    # 测试URL
    url = "https://meeting.tencent.com/cw/lvm1Z7Gj4f"
    
    print(f"\n测试URL: {url}")
    print("创建下载器...")
    
    try:
        # 使用cookies
        downloader = TencentMeetingDownloader(
            headless=False,  # 显示浏览器
            use_cookies=True
        )
        
        print("访问页面...")
        success = downloader.open_meeting_page(url)
        
        if success:
            print("✅ 页面访问成功！")
            
            # 检查页面状态
            page_source = downloader.driver.page_source
            
            if "登录" in page_source or "扫码" in page_source:
                print("❌ 页面仍需要登录，cookies无效")
            else:
                print("✅ 页面已登录，cookies有效！")
                
                if "录制" in page_source:
                    print("✅ 发现录制内容")
                    
                    # 尝试查找下载按钮
                    try:
                        from selenium.webdriver.common.by import By
                        save_btn = downloader.driver.find_element(By.CLASS_NAME, "saveas-btn")
                        print("✅ 找到下载按钮")
                        
                        choice = input("是否尝试下载? (y/n): ").strip().lower()
                        if choice == 'y':
                            print("点击下载按钮...")
                            save_btn.click()
                            time.sleep(3)
                            print("✅ 已点击下载按钮，请在浏览器中选择下载选项")
                            
                    except Exception as e:
                        print(f"⚠️ 未找到下载按钮: {e}")
                else:
                    print("⚠️ 未发现录制内容")
        else:
            print("❌ 页面访问失败")
        
        input("按回车关闭浏览器...")
        downloader.close()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    quick_test()
