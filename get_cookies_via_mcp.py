#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通过MCP Chrome扩展获取腾讯会议cookies
"""

import json
import os
from cookie_manager import <PERSON>ieManager

def get_cookies_from_chrome_mcp():
    """
    通过chrome-mcp-stdio获取当前页面的cookies
    """
    try:
        # 导入chrome-mcp-stdio相关功能
        print("正在通过MCP获取cookies...")
        
        # 使用chrome-mcp-stdio的JavaScript注入功能获取cookies
        js_script = """
        // 获取所有cookies的详细信息
        function getAllCookiesDetailed() {
            const cookies = [];
            const cookieString = document.cookie;
            
            if (cookieString) {
                const cookiePairs = cookieString.split(';');
                for (let pair of cookiePairs) {
                    const [name, value] = pair.trim().split('=');
                    if (name && value) {
                        cookies.push({
                            name: name.trim(),
                            value: value.trim(),
                            domain: window.location.hostname,
                            path: '/',
                            secure: window.location.protocol === 'https:',
                            httpOnly: false  // JavaScript无法获取httpOnly cookies
                        });
                    }
                }
            }
            
            return {
                cookies: cookies,
                domain: window.location.hostname,
                url: window.location.href,
                timestamp: new Date().toISOString(),
                userAgent: navigator.userAgent
            };
        }
        
        // 返回结果
        const result = getAllCookiesDetailed();
        console.log('Cookies获取结果:', result);
        
        // 将结果存储到页面元素中，以便后续获取
        const resultElement = document.createElement('div');
        resultElement.id = 'cookie-result';
        resultElement.style.display = 'none';
        resultElement.textContent = JSON.stringify(result);
        document.body.appendChild(resultElement);
        
        return result;
        """
        
        return js_script
        
    except Exception as e:
        print(f"✗ 生成cookie获取脚本失败: {e}")
        return None

def save_cookies_from_mcp_result(cookie_data):
    """
    保存从MCP获取的cookie数据
    
    Args:
        cookie_data (dict): 从MCP获取的cookie数据
    """
    try:
        manager = CookieManager()
        
        # 转换为Selenium格式的cookies
        selenium_cookies = []
        for cookie in cookie_data.get('cookies', []):
            selenium_cookie = {
                'name': cookie['name'],
                'value': cookie['value'],
                'domain': cookie.get('domain', '.meeting.tencent.com'),
                'path': cookie.get('path', '/'),
                'secure': cookie.get('secure', True),
                'httpOnly': cookie.get('httpOnly', False)
            }
            selenium_cookies.append(selenium_cookie)
        
        # 保存cookies
        cookie_save_data = {
            'cookies': selenium_cookies,
            'saved_time': cookie_data.get('timestamp', ''),
            'domain': cookie_data.get('domain', 'meeting.tencent.com'),
            'source': 'mcp'
        }
        
        with open(manager.cookie_path, 'w', encoding='utf-8') as f:
            json.dump(cookie_save_data, f, ensure_ascii=False, indent=2)
        
        print(f"✓ 通过MCP保存了 {len(selenium_cookies)} 个cookies")
        return True
        
    except Exception as e:
        print(f"✗ 保存MCP cookies失败: {e}")
        return False

def extract_cookies_from_browser():
    """
    从当前浏览器页面提取cookies
    需要在腾讯会议页面运行
    """
    print("Cookie提取指南")
    print("=" * 50)
    print("请按以下步骤操作:")
    print("1. 在浏览器中打开腾讯会议页面并登录")
    print("2. 按F12打开开发者工具")
    print("3. 切换到Console(控制台)标签")
    print("4. 复制并粘贴以下JavaScript代码:")
    print()
    
    js_code = get_cookies_from_chrome_mcp()
    print("```javascript")
    print(js_code)
    print("```")
    print()
    print("5. 按回车执行代码")
    print("6. 复制输出的JSON结果")
    print("7. 将结果保存到文件或直接使用")
    print("=" * 50)

def manual_cookie_input():
    """
    手动输入cookie数据
    """
    print("\n手动Cookie输入模式")
    print("=" * 30)
    
    try:
        print("请粘贴从浏览器获取的cookie JSON数据:")
        print("(输入完成后按回车，然后输入'END'并按回车结束)")
        
        lines = []
        while True:
            line = input()
            if line.strip() == 'END':
                break
            lines.append(line)
        
        cookie_json = '\n'.join(lines)
        cookie_data = json.loads(cookie_json)
        
        if save_cookies_from_mcp_result(cookie_data):
            print("✓ Cookies保存成功！")
            return True
        else:
            print("✗ Cookies保存失败")
            return False
            
    except json.JSONDecodeError:
        print("✗ JSON格式错误，请检查输入的数据")
        return False
    except Exception as e:
        print(f"✗ 处理失败: {e}")
        return False

def test_saved_cookies():
    """
    测试保存的cookies是否有效
    """
    print("\n测试保存的cookies")
    print("=" * 30)
    
    try:
        from tencent_meeting_downloader import TencentMeetingDownloader
        
        downloader = TencentMeetingDownloader(
            headless=False,
            use_cookies=True
        )
        
        test_url = "https://meeting.tencent.com/cw/lvm1Z7Gj4f"
        
        print(f"正在测试URL: {test_url}")
        success = downloader.open_meeting_page(test_url)
        
        if success:
            print("✓ Cookies有效，页面访问成功！")
        else:
            print("✗ Cookies可能无效或页面需要重新登录")
        
        downloader.close()
        return success
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("腾讯会议Cookie获取工具")
    print("=" * 50)
    
    manager = CookieManager()
    
    # 检查现有cookies
    info = manager.get_cookie_info()
    if info:
        print(f"现有cookies信息:")
        print(f"  保存时间: {info['saved_time']}")
        print(f"  存在天数: {info['age_days']} 天")
        print(f"  Cookie数量: {info['cookie_count']}")
        print(f"  是否有效: {'是' if info['is_valid'] else '否'}")
    else:
        print("未找到现有cookies")
    
    print("\n选择操作:")
    print("1. 显示JavaScript代码用于手动获取cookies")
    print("2. 手动输入cookie数据")
    print("3. 测试现有cookies")
    print("4. 清除现有cookies")
    print("5. 启动手动登录模式")
    
    choice = input("\n请选择操作 (1-5): ").strip()
    
    if choice == '1':
        extract_cookies_from_browser()
    elif choice == '2':
        manual_cookie_input()
    elif choice == '3':
        test_saved_cookies()
    elif choice == '4':
        manager.clear_cookies()
    elif choice == '5':
        try:
            from tencent_meeting_downloader import TencentMeetingDownloader
            downloader = TencentMeetingDownloader(headless=False)
            downloader.manual_login_mode()
            downloader.close()
        except Exception as e:
            print(f"✗ 手动登录模式失败: {e}")
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
