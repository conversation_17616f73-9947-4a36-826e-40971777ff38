#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终工作版本的腾讯会议下载器
整合了所有修复，支持cookie自动登录和改进的下载选项识别
"""

import time
import os
from selenium.webdriver.common.by import By
from selenium.common.exceptions import NoSuchElementException
from tencent_meeting_downloader import TencentMeetingDownloader
from cookie_manager import CookieManager

def download_meeting_recording(url=None):
    """
    下载腾讯会议录制文件
    
    Args:
        url (str): 录制文件URL，如果为None则提示用户输入
    """
    print("🚀 腾讯会议录制文件下载器 (最终版)")
    print("=" * 60)
    
    # 获取URL
    if not url:
        url = input("请输入腾讯会议录制文件URL: ").strip()
        if not url:
            print("使用默认测试URL")
            url = "https://meeting.tencent.com/cw/lvm1Z7Gj4f"
    
    print(f"下载URL: {url}")
    
    # 检查cookie状态
    manager = CookieManager()
    has_cookies = manager.is_cookies_valid()
    
    if has_cookies:
        print("✅ 检测到有效cookies，将自动登录")
    else:
        print("⚠️ 未检测到有效cookies，可能需要手动登录")
    
    try:
        # 创建下载器
        downloader = TencentMeetingDownloader(
            headless=False,  # 显示浏览器
            use_cookies=True  # 使用cookies
        )
        
        print("\n步骤1: 访问页面...")
        success = downloader.open_meeting_page(url)
        
        if not success:
            print("❌ 页面访问失败")
            downloader.close()
            return False
        
        print("✅ 页面访问成功")
        
        # 检查是否需要手动登录
        page_source = downloader.driver.page_source
        needs_login = any(indicator in page_source.lower() for indicator in 
                         ["登录", "login", "扫码", "qr", "二维码"])
        
        if needs_login:
            print("\n⚠️ 检测到需要登录")
            print("请在浏览器中扫码登录，然后按回车继续...")
            input("登录完成后按回车...")
            
            # 重新保存cookies
            downloader.cookie_manager.save_cookies(downloader.driver)
            print("✅ 登录状态已更新")
        
        print("\n步骤2: 查找下载按钮...")
        
        # 查找下载按钮
        try:
            save_btn = downloader.driver.find_element(By.CLASS_NAME, "saveas-btn")
            print("✅ 找到下载按钮")
        except NoSuchElementException:
            print("❌ 未找到下载按钮")
            downloader.close()
            return False
        
        print("\n步骤3: 点击下载按钮...")
        save_btn.click()
        
        # 等待下载菜单出现
        time.sleep(3)
        
        print("\n步骤4: 选择下载选项...")
        
        # 改进的下载选项选择
        download_success = False
        
        # 策略1: 查找视频相关选项
        video_keywords = ["视频内容", "视频", "智能优化版", "原始版"]
        for keyword in video_keywords:
            try:
                elements = downloader.driver.find_elements(By.XPATH, f"//*[contains(text(), '{keyword}')]")
                for elem in elements:
                    if elem.is_displayed() and elem.is_enabled():
                        print(f"✅ 选择下载选项: {keyword}")
                        elem.click()
                        download_success = True
                        break
                if download_success:
                    break
            except Exception as e:
                continue
        
        # 策略2: 如果没找到视频选项，尝试第一个可用选项
        if not download_success:
            try:
                # 查找所有可能的下载选项
                selectors = [
                    "//div[contains(@class, 'dropdown-item')]",
                    "//li[contains(@class, 'dropdown-item')]",
                    "//div[contains(@class, 'menu-item')]",
                    "//li[contains(@class, 'menu-item')]"
                ]
                
                for selector in selectors:
                    elements = downloader.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        if elem.is_displayed() and elem.text.strip():
                            print(f"✅ 选择下载选项: {elem.text}")
                            elem.click()
                            download_success = True
                            break
                    if download_success:
                        break
            except Exception as e:
                print(f"策略2失败: {e}")
        
        if download_success:
            print("✅ 下载选项已选择")
            print("\n步骤5: 等待下载开始...")
            
            # 等待下载开始
            time.sleep(5)
            
            # 检查下载目录
            download_dir = downloader.download_dir
            print(f"下载目录: {download_dir}")
            
            # 检查是否有新文件
            files = os.listdir(download_dir)
            media_files = [f for f in files if f.endswith(('.mp4', '.avi', '.mov', '.mp3', '.wav', '.m4a'))]
            download_files = [f for f in files if f.endswith('.crdownload')]
            
            if media_files:
                print(f"✅ 发现 {len(media_files)} 个媒体文件:")
                for file in media_files:
                    file_size = os.path.getsize(os.path.join(download_dir, file)) / (1024*1024)
                    print(f"  📁 {file} ({file_size:.1f} MB)")
            
            if download_files:
                print(f"⏳ 发现 {len(download_files)} 个正在下载的文件:")
                for file in download_files:
                    print(f"  ⬇️ {file}")
            
            if media_files or download_files:
                print("\n✅ 下载成功启动！")
                print("提示:")
                print("- 完整的文件已下载完成")
                print("- .crdownload文件表示正在下载中")
                print("- 请保持浏览器打开直到下载完成")
                
                choice = input("\n是否保持浏览器打开监控下载? (y/n): ").strip().lower()
                if choice == 'y':
                    print("浏览器将保持打开，您可以:")
                    print("1. 监控下载进度")
                    print("2. 下载其他内容")
                    print("3. 手动关闭浏览器")
                    input("按回车关闭程序（浏览器将继续运行）...")
                    return True
            else:
                print("⚠️ 未检测到下载文件")
                print("可能的原因:")
                print("1. 下载尚未开始")
                print("2. 文件下载到了其他位置")
                print("3. 需要手动确认下载")
        else:
            print("❌ 未能选择下载选项")
            print("请手动在浏览器中选择下载选项")
        
        input("按回车关闭浏览器...")
        downloader.close()
        return download_success
        
    except Exception as e:
        print(f"❌ 下载过程失败: {e}")
        return False

def batch_download():
    """批量下载多个录制文件"""
    print("📦 批量下载模式")
    print("=" * 40)
    
    urls = []
    print("请输入要下载的URL列表（每行一个，输入空行结束）:")
    
    while True:
        url = input("URL: ").strip()
        if not url:
            break
        urls.append(url)
    
    if not urls:
        print("未输入任何URL")
        return
    
    print(f"\n准备下载 {len(urls)} 个录制文件")
    
    success_count = 0
    for i, url in enumerate(urls, 1):
        print(f"\n{'='*20} 下载 {i}/{len(urls)} {'='*20}")
        if download_meeting_recording(url):
            success_count += 1
        
        if i < len(urls):
            choice = input("是否继续下载下一个? (y/n): ").strip().lower()
            if choice != 'y':
                break
    
    print(f"\n批量下载完成: {success_count}/{len(urls)} 成功")

def main():
    """主菜单"""
    print("🎯 腾讯会议录制文件下载器")
    print("=" * 60)
    
    # 检查cookie状态
    manager = CookieManager()
    if manager.is_cookies_valid():
        print("✅ 检测到有效登录状态")
    else:
        print("⚠️ 未检测到登录状态，首次使用需要登录")
    
    print("\n选择操作:")
    print("1. 下载单个录制文件")
    print("2. 批量下载多个录制文件")
    print("3. 测试cookie功能")
    print("4. 退出")
    
    choice = input("\n请选择 (1-4): ").strip()
    
    if choice == '1':
        download_meeting_recording()
    elif choice == '2':
        batch_download()
    elif choice == '3':
        os.system("python quick_cookie_test.py")
    elif choice == '4':
        print("退出程序")
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
