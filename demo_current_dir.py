#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示当前目录下载功能
展示下载器如何使用当前工作目录
"""

import os
import tempfile
import shutil

def demo_current_directory():
    """演示当前目录功能"""
    print("腾讯会议下载器 - 当前目录功能演示")
    print("=" * 50)
    
    # 显示当前目录
    original_dir = os.getcwd()
    print(f"原始工作目录: {original_dir}")
    
    # 创建临时目录进行演示
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"临时演示目录: {temp_dir}")
        
        # 切换到临时目录
        os.chdir(temp_dir)
        print(f"切换到目录: {os.getcwd()}")
        
        try:
            # 导入下载器
            import sys
            sys.path.append(original_dir)
            from tencent_meeting_downloader import TencentMeetingDownloader
            
            print("\n创建下载器实例...")
            downloader = TencentMeetingDownloader(
                headless=True,
                download_dir=None  # 不指定目录，使用当前目录
            )
            
            print(f"下载器目录: {downloader.download_dir}")
            print(f"当前工作目录: {os.getcwd()}")
            
            if downloader.download_dir == os.getcwd():
                print("✓ 成功：下载器正确使用当前目录")
            else:
                print("✗ 失败：下载器未使用当前目录")
            
            # 检查是否创建了logs目录
            logs_dir = os.path.join(temp_dir, "logs")
            if os.path.exists(logs_dir):
                print(f"✓ 日志目录已创建: {logs_dir}")
            else:
                print("✗ 日志目录未创建")
            
            downloader.close()
            
        except Exception as e:
            print(f"✗ 演示失败: {e}")
        
        finally:
            # 恢复原始目录
            os.chdir(original_dir)
            print(f"\n恢复到原始目录: {os.getcwd()}")

def show_usage_examples():
    """显示使用示例"""
    print("\n" + "=" * 50)
    print("使用示例")
    print("=" * 50)
    
    print("\n1. 在任意目录运行GUI:")
    print("   cd D:\\MyDownloads")
    print("   python D:\\path\\to\\download_gui.py")
    print("   # 文件将下载到 D:\\MyDownloads")
    
    print("\n2. 在任意目录运行CLI:")
    print("   cd C:\\Users\\<USER>\\Desktop")
    print("   python D:\\path\\to\\download_cli.py https://meeting.tencent.com/cw/xxx")
    print("   # 文件将下载到桌面")
    
    print("\n3. 编程接口使用:")
    print("   import os")
    print("   os.chdir('D:\\\\MyFolder')")
    print("   from tencent_meeting_downloader import TencentMeetingDownloader")
    print("   downloader = TencentMeetingDownloader()")
    print("   # 文件将下载到 D:\\MyFolder")
    
    print("\n4. 指定特定目录:")
    print("   downloader = TencentMeetingDownloader(")
    print("       download_dir='D:\\\\SpecificFolder'")
    print("   )")
    print("   # 文件将下载到指定目录")

def main():
    """主函数"""
    demo_current_directory()
    show_usage_examples()
    
    print("\n" + "=" * 50)
    print("总结")
    print("=" * 50)
    print("✓ 下载器默认使用当前工作目录")
    print("✓ 可以通过cd命令切换下载位置")
    print("✓ 也可以手动指定download_dir参数")
    print("✓ GUI界面默认显示当前目录，可手动修改")
    print("✓ 所有下载的文件将保存在指定目录")
    
    print("\n🎉 当前目录功能配置完成！")

if __name__ == "__main__":
    main()
