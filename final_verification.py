#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终功能验证脚本
验证腾讯会议录制内容下载器的所有功能是否完整
"""

import os
import sys

def check_file_exists(filename, description):
    """检查文件是否存在"""
    if os.path.exists(filename):
        print(f"✓ {description}: {filename}")
        return True
    else:
        print(f"✗ {description}: {filename} (缺失)")
        return False

def check_module_import(module_name, description):
    """检查模块是否可以导入"""
    try:
        __import__(module_name)
        print(f"✓ {description}: {module_name}")
        return True
    except ImportError as e:
        print(f"✗ {description}: {module_name} (导入失败: {e})")
        return False

def check_function_exists(module_name, function_name, description):
    """检查模块中的函数是否存在"""
    try:
        module = __import__(module_name)
        if hasattr(module, function_name):
            print(f"✓ {description}: {module_name}.{function_name}")
            return True
        else:
            print(f"✗ {description}: {module_name}.{function_name} (函数不存在)")
            return False
    except ImportError as e:
        print(f"✗ {description}: {module_name}.{function_name} (模块导入失败: {e})")
        return False

def main():
    """主验证函数"""
    print("腾讯会议录制内容下载器 - 最终功能验证")
    print("=" * 50)
    
    total_checks = 0
    passed_checks = 0
    
    # 1. 检查核心文件
    print("\n1. 核心文件检查:")
    core_files = [
        ("tencent_meeting_downloader.py", "主下载器"),
        ("config.py", "配置文件"),
        ("requirements.txt", "依赖列表"),
    ]
    
    for filename, desc in core_files:
        total_checks += 1
        if check_file_exists(filename, desc):
            passed_checks += 1
    
    # 2. 检查用户界面文件
    print("\n2. 用户界面文件检查:")
    ui_files = [
        ("download_gui.py", "图形界面"),
        ("download_cli.py", "命令行工具"),
        ("download_example.py", "使用示例"),
    ]
    
    for filename, desc in ui_files:
        total_checks += 1
        if check_file_exists(filename, desc):
            passed_checks += 1
    
    # 3. 检查工具脚本
    print("\n3. 工具脚本检查:")
    tool_files = [
        ("install.py", "安装脚本"),
        ("test_downloader.py", "测试脚本"),
        ("run_gui.bat", "Windows启动脚本"),
        ("run_gui.sh", "Linux/macOS启动脚本"),
    ]
    
    for filename, desc in tool_files:
        total_checks += 1
        if check_file_exists(filename, desc):
            passed_checks += 1
    
    # 4. 检查文档
    print("\n4. 文档文件检查:")
    doc_files = [
        ("README.md", "使用说明"),
        ("项目完成总结.md", "项目总结"),
    ]
    
    for filename, desc in doc_files:
        total_checks += 1
        if check_file_exists(filename, desc):
            passed_checks += 1
    
    # 5. 检查模块导入
    print("\n5. 模块导入检查:")
    modules = [
        ("selenium", "Selenium自动化库"),
        ("requests", "HTTP请求库"),
        ("webdriver_manager", "WebDriver管理器"),
    ]
    
    for module_name, desc in modules:
        total_checks += 1
        if check_module_import(module_name, desc):
            passed_checks += 1
    
    # 6. 检查自定义模块
    print("\n6. 自定义模块检查:")
    custom_modules = [
        ("tencent_meeting_downloader", "主下载器模块"),
        ("config", "配置模块"),
        ("download_gui", "GUI模块"),
        ("download_cli", "CLI模块"),
    ]
    
    for module_name, desc in custom_modules:
        total_checks += 1
        if check_module_import(module_name, desc):
            passed_checks += 1
    
    # 7. 检查核心功能
    print("\n7. 核心功能检查:")
    try:
        from tencent_meeting_downloader import TencentMeetingDownloader
        from config import get_config, get_content_type_name
        
        # 检查下载器类的关键方法
        downloader_methods = [
            "download_video_content",
            "download_audio_content", 
            "download_transcript",
            "download_summary",
            "download_all_content"
        ]
        
        for method in downloader_methods:
            total_checks += 1
            if hasattr(TencentMeetingDownloader, method):
                print(f"✓ 下载器方法: {method}")
                passed_checks += 1
            else:
                print(f"✗ 下载器方法: {method} (不存在)")
        
        # 检查配置功能
        total_checks += 1
        try:
            config = get_config()
            if hasattr(config, 'ALL_CONTENT_TYPES') and len(config.ALL_CONTENT_TYPES) == 8:
                print(f"✓ 配置功能: 支持{len(config.ALL_CONTENT_TYPES)}种内容类型")
                passed_checks += 1
            else:
                print("✗ 配置功能: 内容类型定义不完整")
        except Exception as e:
            print(f"✗ 配置功能: 加载失败 ({e})")
        
        # 检查内容类型名称映射
        total_checks += 1
        try:
            name = get_content_type_name('video')
            if name == '视频内容':
                print("✓ 内容类型映射: 中文名称正确")
                passed_checks += 1
            else:
                print(f"✗ 内容类型映射: 名称错误 (期望'视频内容', 得到'{name}')")
        except Exception as e:
            print(f"✗ 内容类型映射: 功能异常 ({e})")
            
    except ImportError as e:
        print(f"✗ 核心功能检查失败: 无法导入模块 ({e})")
        total_checks += 7  # 跳过的检查项数量
    
    # 8. 检查内容类型完整性
    print("\n8. 内容类型完整性检查:")
    expected_types = [
        'video', 'audio', 'transcript_original', 'transcript_optimized',
        'summary_topic', 'summary_chapter', 'summary_speaker', 'summary_deepseek'
    ]
    
    try:
        from config import get_config
        config = get_config()
        
        for content_type in expected_types:
            total_checks += 1
            if content_type in config.ALL_CONTENT_TYPES:
                print(f"✓ 内容类型: {content_type}")
                passed_checks += 1
            else:
                print(f"✗ 内容类型: {content_type} (缺失)")
                
    except Exception as e:
        print(f"✗ 内容类型检查失败: {e}")
        total_checks += len(expected_types)
    
    # 总结
    print("\n" + "=" * 50)
    print(f"验证结果: {passed_checks}/{total_checks} 项通过")
    
    success_rate = (passed_checks / total_checks) * 100 if total_checks > 0 else 0
    print(f"成功率: {success_rate:.1f}%")
    
    if success_rate >= 95:
        print("\n🎉 项目验证通过！所有核心功能完整。")
        print("\n可以使用的启动方式:")
        print("1. 图形界面: python download_gui.py")
        print("2. 命令行: python download_cli.py <URL> --types video audio")
        print("3. 编程接口: python download_example.py")
        print("4. Windows快捷方式: run_gui.bat")
        print("5. Linux/macOS脚本: ./run_gui.sh")
        return True
    elif success_rate >= 80:
        print("\n⚠️ 项目基本完整，但有部分功能可能需要检查。")
        print("建议运行: python install.py")
        return False
    else:
        print("\n❌ 项目存在重要缺失，需要重新安装或修复。")
        print("请检查错误信息并重新运行安装程序。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
