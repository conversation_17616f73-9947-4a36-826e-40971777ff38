#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复页面跳转和连接问题
解决需要跳转到会议链接才有下载按钮的问题
"""

import time
import os
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
from tencent_meeting_downloader import TencentMeetingDownloader
from cookie_manager import CookieManager

def improved_navigation_flow(url):
    """
    改进的导航流程
    确保正确跳转到会议页面并找到下载按钮
    """
    print("🔄 改进的页面导航流程")
    print("=" * 50)
    
    try:
        # 创建下载器
        downloader = TencentMeetingDownloader(
            headless=False,
            use_cookies=True
        )
        
        print("步骤1: 检查登录状态...")
        
        # 首先访问腾讯会议主页确保登录状态
        print("访问腾讯会议主页...")
        downloader.driver.get("https://meeting.tencent.com")
        time.sleep(3)
        
        # 检查是否已登录
        page_source = downloader.driver.page_source
        if "登录" in page_source or "扫码" in page_source:
            print("⚠️ 需要登录，请在浏览器中扫码登录")
            input("登录完成后按回车继续...")
            
            # 保存新的cookies
            downloader.cookie_manager.save_cookies(downloader.driver)
            print("✅ 登录状态已保存")
        else:
            print("✅ 已登录状态")
        
        print(f"\n步骤2: 跳转到会议页面...")
        print(f"目标URL: {url}")
        
        # 直接跳转到会议页面
        downloader.driver.get(url)
        time.sleep(5)  # 等待页面完全加载
        
        print("步骤3: 检查页面内容...")
        
        # 检查页面标题
        title = downloader.driver.title
        print(f"页面标题: {title}")
        
        # 检查页面内容
        page_source = downloader.driver.page_source
        
        # 检查是否有录制内容
        content_indicators = ["录制", "视频", "音频", "另存为", "下载"]
        found_content = []
        for indicator in content_indicators:
            if indicator in page_source:
                found_content.append(indicator)
        
        if found_content:
            print(f"✅ 发现内容: {', '.join(found_content)}")
        else:
            print("⚠️ 未发现录制内容")
        
        print("\n步骤4: 查找下载按钮...")
        
        # 尝试多种方式查找下载按钮
        download_button = None
        button_selectors = [
            (By.CLASS_NAME, "saveas-btn"),
            (By.XPATH, "//button[contains(text(), '另存为')]"),
            (By.XPATH, "//button[contains(text(), '下载')]"),
            (By.XPATH, "//div[contains(@class, 'saveas')]"),
            (By.XPATH, "//a[contains(text(), '下载')]"),
            (By.CSS_SELECTOR, "[class*='save']"),
            (By.CSS_SELECTOR, "[class*='download']"),
        ]
        
        for selector_type, selector_value in button_selectors:
            try:
                elements = downloader.driver.find_elements(selector_type, selector_value)
                for element in elements:
                    if element.is_displayed():
                        download_button = element
                        print(f"✅ 找到下载按钮: {selector_value}")
                        break
                if download_button:
                    break
            except Exception as e:
                continue
        
        if not download_button:
            print("❌ 未找到下载按钮")
            print("可能的原因:")
            print("1. 页面尚未完全加载")
            print("2. 需要特殊权限")
            print("3. 录制文件不支持下载")
            
            # 保存页面截图用于分析
            try:
                screenshot_path = "navigation_issue_screenshot.png"
                downloader.driver.save_screenshot(screenshot_path)
                print(f"📸 页面截图已保存: {screenshot_path}")
                
                # 保存页面源码
                with open("navigation_issue_source.html", "w", encoding="utf-8") as f:
                    f.write(page_source)
                print("📄 页面源码已保存: navigation_issue_source.html")
            except Exception as e:
                print(f"保存诊断信息失败: {e}")
            
            print("\n请手动检查浏览器中的页面")
            input("检查完成后按回车继续...")
            
            downloader.close()
            return False
        
        print("\n步骤5: 测试下载功能...")
        
        try:
            # 点击下载按钮
            download_button.click()
            print("✅ 下载按钮已点击")
            
            # 等待下载菜单出现
            time.sleep(3)
            
            # 查找下载选项
            print("查找下载选项...")
            
            download_options = []
            option_selectors = [
                "//div[contains(text(), '视频')]",
                "//span[contains(text(), '视频')]",
                "//li[contains(text(), '视频')]",
                "//div[contains(text(), '音频')]",
                "//span[contains(text(), '音频')]",
                "//div[contains(@class, 'dropdown-item')]",
                "//li[contains(@class, 'dropdown-item')]",
                "//div[contains(@class, 'menu-item')]",
            ]
            
            for selector in option_selectors:
                try:
                    elements = downloader.driver.find_elements(By.XPATH, selector)
                    for elem in elements:
                        if elem.is_displayed() and elem.text.strip():
                            download_options.append((elem, elem.text.strip()))
                except:
                    continue
            
            if download_options:
                print(f"✅ 找到 {len(download_options)} 个下载选项:")
                for i, (elem, text) in enumerate(download_options):
                    print(f"  {i+1}. {text}")
                
                # 尝试点击第一个选项
                try:
                    download_options[0][0].click()
                    print(f"✅ 已选择: {download_options[0][1]}")
                    
                    # 等待下载开始
                    time.sleep(5)
                    
                    # 检查下载目录
                    files = os.listdir(downloader.download_dir)
                    media_files = [f for f in files if f.endswith(('.mp4', '.avi', '.mov', '.mp3', '.wav', '.m4a'))]
                    download_files = [f for f in files if f.endswith('.crdownload')]
                    
                    if media_files or download_files:
                        print("✅ 下载已开始!")
                        if media_files:
                            print(f"完成的文件: {len(media_files)} 个")
                        if download_files:
                            print(f"下载中的文件: {len(download_files)} 个")
                    else:
                        print("⚠️ 未检测到下载文件，可能需要更多时间")
                    
                except Exception as e:
                    print(f"点击下载选项失败: {e}")
            else:
                print("❌ 未找到下载选项")
            
        except Exception as e:
            print(f"下载测试失败: {e}")
        
        print("\n步骤6: 保持浏览器打开以便手动操作...")
        input("按回车关闭浏览器...")
        
        downloader.close()
        return True
        
    except Exception as e:
        print(f"❌ 导航流程失败: {e}")
        return False

def test_connection_stability():
    """测试连接稳定性"""
    print("\n🔗 测试WebDriver连接稳定性")
    print("=" * 50)
    
    try:
        print("创建WebDriver实例...")
        downloader = TencentMeetingDownloader(
            headless=True,  # 无头模式快速测试
            use_cookies=False
        )
        
        print("✅ WebDriver创建成功")
        
        # 测试基本导航
        print("测试基本导航...")
        downloader.driver.get("https://www.baidu.com")
        time.sleep(2)
        
        title = downloader.driver.title
        print(f"✅ 导航成功，页面标题: {title}")
        
        # 测试腾讯会议页面
        print("测试腾讯会议页面...")
        downloader.driver.get("https://meeting.tencent.com")
        time.sleep(3)
        
        title = downloader.driver.title
        print(f"✅ 腾讯会议页面访问成功，标题: {title}")
        
        downloader.close()
        print("✅ 连接稳定性测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 连接稳定性测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 页面导航和连接问题修复工具")
    print("=" * 60)
    
    # 首先测试连接稳定性
    if not test_connection_stability():
        print("❌ WebDriver连接不稳定，请检查:")
        print("1. Chrome浏览器是否正常安装")
        print("2. ChromeDriver是否正确配置")
        print("3. 是否有其他程序占用端口")
        print("4. 防火墙或安全软件是否阻止")
        return
    
    # 获取测试URL
    url = input("\n请输入腾讯会议录制文件URL (回车使用默认): ").strip()
    if not url:
        url = "https://meeting.tencent.com/cw/lvm1Z7Gj4f"
    
    print(f"测试URL: {url}")
    
    # 执行改进的导航流程
    success = improved_navigation_flow(url)
    
    if success:
        print("\n✅ 导航流程测试完成")
        print("如果下载成功，说明问题已解决")
    else:
        print("\n❌ 导航流程存在问题")
        print("建议:")
        print("1. 检查录制文件URL是否正确")
        print("2. 确认有访问权限")
        print("3. 尝试在浏览器中手动访问")

if __name__ == "__main__":
    main()
