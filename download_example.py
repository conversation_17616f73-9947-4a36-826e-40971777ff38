#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
腾讯会议录制内容下载示例
简化版使用示例
"""

from tencent_meeting_downloader import TencentMeetingDownloader

def download_meeting_content():
    """下载会议内容的简单示例"""
    
    # 会议录制页面URL - 请替换为您的实际URL
    meeting_url = "https://meeting.tencent.com/cw/lvm1Z7Gj4f"
    
    # 创建下载器
    downloader = TencentMeetingDownloader(
        headless=False,  # 设置为True可以后台运行（无界面）
        download_dir=None  # 下载到当前目录
    )
    
    try:
        print("=== 腾讯会议录制内容下载器 ===")
        print(f"目标URL: {meeting_url}")
        print(f"下载目录: {downloader.download_dir}")
        print()
        
        # 选择要下载的内容类型
        # 可以根据需要注释掉不需要的类型
        content_types = [
            'video',                # 视频内容
            'audio',                # 纯音频文件
            'transcript_original',  # 原文版转写文本
            # 'transcript_optimized', # 智能优化版转写文本
            'summary_topic',        # 主题摘要
            # 'summary_chapter',      # 分章节小结
            # 'summary_speaker',      # 发言人观点
            # 'summary_deepseek'      # DeepSeek摘要
        ]
        
        print("将下载以下内容类型:")
        for content_type in content_types:
            type_names = {
                'video': '视频内容',
                'audio': '纯音频文件', 
                'transcript_original': '原文版转写文本',
                'transcript_optimized': '智能优化版转写文本',
                'summary_topic': '主题摘要',
                'summary_chapter': '分章节小结',
                'summary_speaker': '发言人观点',
                'summary_deepseek': 'DeepSeek摘要'
            }
            print(f"  - {type_names.get(content_type, content_type)}")
        
        print("\n开始下载...")
        
        # 执行下载
        downloader.download_all_content(meeting_url, content_types)
        
        print("\n=== 下载完成 ===")
        print(f"请检查下载目录: {downloader.download_dir}")
        
    except KeyboardInterrupt:
        print("\n\n用户取消下载")
    except Exception as e:
        print(f"\n下载过程中出现错误: {e}")
        print("请检查:")
        print("1. 网络连接是否正常")
        print("2. URL是否正确")
        print("3. 是否有访问权限")
        print("4. Chrome浏览器是否已安装")
    finally:
        downloader.close()

def download_specific_content():
    """下载特定类型内容的示例"""
    
    meeting_url = "https://meeting.tencent.com/cw/lvm1Z7Gj4f"
    
    downloader = TencentMeetingDownloader(headless=False)
    
    try:
        # 只下载视频和音频
        downloader.download_all_content(meeting_url, ['video', 'audio'])
        
    except Exception as e:
        print(f"下载失败: {e}")
    finally:
        downloader.close()

def download_text_only():
    """只下载文本内容的示例"""
    
    meeting_url = "https://meeting.tencent.com/cw/lvm1Z7Gj4f"
    
    downloader = TencentMeetingDownloader(headless=False)
    
    try:
        # 只下载文本相关内容
        text_types = [
            'transcript_original',
            'transcript_optimized', 
            'summary_topic',
            'summary_chapter',
            'summary_speaker',
            'summary_deepseek'
        ]
        
        downloader.download_all_content(meeting_url, text_types)
        
    except Exception as e:
        print(f"下载失败: {e}")
    finally:
        downloader.close()

if __name__ == "__main__":
    # 运行主要的下载示例
    download_meeting_content()
    
    # 如果需要运行其他示例，可以取消注释下面的行
    # download_specific_content()
    # download_text_only()
