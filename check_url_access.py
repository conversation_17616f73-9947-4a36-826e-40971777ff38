#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查腾讯会议URL访问情况
"""

import requests
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.chrome.service import Service

def check_url_with_requests():
    """使用requests检查URL"""
    print("使用requests检查URL访问")
    print("=" * 30)
    
    url = "https://meeting.tencent.com/cw/lvm1Z7Gj4f"
    
    try:
        response = requests.get(url, timeout=10)
        print(f"状态码: {response.status_code}")
        print(f"响应长度: {len(response.text)} 字符")
        
        if "登录" in response.text:
            print("⚠️ 页面包含登录相关内容")
        if "权限" in response.text:
            print("⚠️ 页面包含权限相关内容")
        if "另存为" in response.text:
            print("✓ 页面包含另存为功能")
        if "录制" in response.text:
            print("✓ 页面包含录制相关内容")
        
        return response.status_code == 200
        
    except Exception as e:
        print(f"✗ 请求失败: {e}")
        return False

def check_url_with_selenium():
    """使用Selenium检查URL"""
    print("\n使用Selenium检查URL访问")
    print("=" * 30)
    
    url = "https://meeting.tencent.com/cw/lvm1Z7Gj4f"
    
    try:
        # 设置Chrome选项
        chrome_options = Options()
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        
        # 创建浏览器实例
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        print(f"正在访问: {url}")
        driver.get(url)
        
        # 等待页面加载
        time.sleep(5)
        
        # 获取页面信息
        title = driver.title
        print(f"页面标题: {title}")
        
        # 检查页面内容
        page_source = driver.page_source
        
        if "登录" in page_source or "login" in page_source.lower():
            print("⚠️ 页面需要登录")
        
        if "权限" in page_source or "permission" in page_source.lower():
            print("⚠️ 页面有权限限制")
        
        if "另存为" in page_source:
            print("✓ 页面包含另存为功能")
        
        if "录制" in page_source:
            print("✓ 页面包含录制内容")
        
        if "视频" in page_source:
            print("✓ 页面包含视频相关内容")
        
        # 检查特定元素
        try:
            save_btn = driver.find_element(By.CLASS_NAME, "saveas-btn")
            print("✓ 找到另存为按钮")
        except:
            print("✗ 未找到另存为按钮")
        
        try:
            dropdown = driver.find_element(By.CLASS_NAME, "dropdown-for-download")
            print("✓ 找到下载下拉菜单")
        except:
            print("✗ 未找到下载下拉菜单")
        
        # 保存页面截图
        screenshot_path = "page_status.png"
        driver.save_screenshot(screenshot_path)
        print(f"已保存页面截图: {screenshot_path}")
        
        # 保存页面源码
        with open("page_source.html", "w", encoding="utf-8") as f:
            f.write(page_source)
        print("已保存页面源码: page_source.html")
        
        driver.quit()
        return True
        
    except Exception as e:
        print(f"✗ Selenium检查失败: {e}")
        return False

def analyze_page_content():
    """分析页面内容"""
    print("\n分析页面内容")
    print("=" * 30)
    
    try:
        with open("page_source.html", "r", encoding="utf-8") as f:
            content = f.read()
        
        print(f"页面内容长度: {len(content)} 字符")
        
        # 检查关键词
        keywords = ["另存为", "下载", "视频", "音频", "录制", "登录", "权限", "过期"]
        for keyword in keywords:
            count = content.count(keyword)
            if count > 0:
                print(f"'{keyword}': 出现 {count} 次")
        
        # 检查是否有错误信息
        error_keywords = ["错误", "error", "失败", "failed", "过期", "expired", "无权限", "unauthorized"]
        for keyword in error_keywords:
            if keyword in content.lower():
                print(f"⚠️ 发现错误相关内容: {keyword}")
        
        return True
        
    except Exception as e:
        print(f"✗ 分析失败: {e}")
        return False

def main():
    """主函数"""
    print("腾讯会议URL访问检查")
    print("=" * 50)
    
    # 1. 使用requests检查
    requests_ok = check_url_with_requests()
    
    # 2. 使用Selenium检查
    selenium_ok = check_url_with_selenium()
    
    # 3. 分析页面内容
    if selenium_ok:
        analyze_page_content()
    
    print("\n" + "=" * 50)
    print("检查结果总结:")
    print(f"Requests访问: {'成功' if requests_ok else '失败'}")
    print(f"Selenium访问: {'成功' if selenium_ok else '失败'}")
    
    if not requests_ok and not selenium_ok:
        print("\n可能的问题:")
        print("1. 网络连接问题")
        print("2. URL已失效")
        print("3. 需要VPN或特殊网络环境")
    elif requests_ok and not selenium_ok:
        print("\n可能的问题:")
        print("1. Chrome浏览器配置问题")
        print("2. Selenium驱动问题")
    elif not requests_ok and selenium_ok:
        print("\n可能的问题:")
        print("1. 页面需要JavaScript渲染")
        print("2. 反爬虫机制")
    else:
        print("\n✓ URL访问正常，请检查页面内容和权限")

if __name__ == "__main__":
    main()
