#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复下载选项识别问题
专门解决点击下载按钮后找不到下载选项的问题
"""

import time
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from tencent_meeting_downloader import TencentMeetingDownloader

def analyze_download_menu():
    """分析下载菜单结构"""
    print("🔍 分析下载菜单结构")
    print("=" * 50)
    
    url = "https://meeting.tencent.com/cw/lvm1Z7Gj4f"
    
    try:
        downloader = TencentMeetingDownloader(
            headless=False,  # 显示浏览器
            use_cookies=True
        )
        
        print("1. 访问页面...")
        success = downloader.open_meeting_page(url)
        
        if not success:
            print("❌ 页面访问失败")
            return
        
        print("✅ 页面访问成功")
        
        # 查找下载按钮
        print("\n2. 查找下载按钮...")
        try:
            save_btn = downloader.driver.find_element(By.CLASS_NAME, "saveas-btn")
            print("✅ 找到下载按钮")
        except NoSuchElementException:
            print("❌ 未找到下载按钮")
            downloader.close()
            return
        
        print("\n3. 点击下载按钮...")
        save_btn.click()
        
        # 等待菜单出现
        print("等待下载菜单出现...")
        time.sleep(3)
        
        print("\n4. 分析页面结构...")
        
        # 获取页面源码
        page_source = downloader.driver.page_source
        
        # 查找所有可能的下载选项
        print("查找所有可能的下载选项:")
        
        # 方法1: 通过文本内容查找
        text_options = [
            "视频内容", "音频内容", "转录内容", "摘要内容",
            "视频", "音频", "转录", "摘要", "文档", "聊天记录"
        ]
        
        found_options = []
        for option in text_options:
            if option in page_source:
                print(f"  ✓ 发现选项文本: {option}")
                found_options.append(option)
        
        # 方法2: 查找所有可点击元素
        print("\n查找所有可点击的下载相关元素:")
        
        clickable_selectors = [
            "//div[contains(@class, 'dropdown-item')]",
            "//li[contains(@class, 'dropdown-item')]", 
            "//div[contains(@class, 'menu-item')]",
            "//li[contains(@class, 'menu-item')]",
            "//div[contains(@class, 'download')]",
            "//li[contains(@class, 'download')]",
            "//ul[@class='dropdown-menu']//li",
            "//div[@class='dropdown-menu']//div",
            "//div[contains(@role, 'menuitem')]",
            "//li[contains(@role, 'menuitem')]"
        ]
        
        clickable_elements = []
        for selector in clickable_selectors:
            try:
                elements = downloader.driver.find_elements(By.XPATH, selector)
                for elem in elements:
                    if elem.is_displayed() and elem.text.strip():
                        clickable_elements.append((selector, elem.text.strip()))
                        print(f"  ✓ 可点击元素: {elem.text.strip()} (选择器: {selector})")
            except Exception as e:
                continue
        
        # 方法3: 查找所有包含特定文本的元素
        print("\n查找包含下载相关文本的所有元素:")
        
        for option in text_options:
            try:
                elements = downloader.driver.find_elements(By.XPATH, f"//*[contains(text(), '{option}')]")
                for elem in elements:
                    if elem.is_displayed():
                        tag_name = elem.tag_name
                        class_name = elem.get_attribute('class') or 'no-class'
                        print(f"  ✓ 找到 '{option}': <{tag_name}> class='{class_name}'")
            except Exception as e:
                continue
        
        # 方法4: 截图保存当前状态
        print("\n5. 保存当前状态...")
        try:
            screenshot_path = "download_menu_analysis.png"
            downloader.driver.save_screenshot(screenshot_path)
            print(f"✅ 截图已保存: {screenshot_path}")
            
            # 保存页面源码
            with open("download_menu_source.html", "w", encoding="utf-8") as f:
                f.write(page_source)
            print("✅ 页面源码已保存: download_menu_source.html")
            
        except Exception as e:
            print(f"⚠️ 保存失败: {e}")
        
        # 让用户手动检查
        print("\n6. 手动检查:")
        print("请在浏览器中查看下载菜单，并告诉我您看到了什么选项")
        print("常见的选项可能包括:")
        print("- 视频内容")
        print("- 音频内容") 
        print("- 转录内容")
        print("- 摘要内容")
        print("- 聊天记录")
        
        input("检查完成后按回车继续...")
        
        downloader.close()
        
        # 总结发现的选项
        print("\n📋 分析总结:")
        print(f"发现的文本选项: {found_options}")
        print(f"发现的可点击元素: {len(clickable_elements)} 个")
        
        if found_options or clickable_elements:
            print("✅ 找到了下载选项，问题可能在于选择器不准确")
        else:
            print("❌ 未找到明显的下载选项，可能需要其他方法")
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")

def test_improved_download():
    """测试改进的下载方法"""
    print("\n🚀 测试改进的下载方法")
    print("=" * 50)
    
    url = "https://meeting.tencent.com/cw/lvm1Z7Gj4f"
    
    try:
        downloader = TencentMeetingDownloader(
            headless=False,
            use_cookies=True
        )
        
        print("访问页面...")
        success = downloader.open_meeting_page(url)
        
        if not success:
            print("❌ 页面访问失败")
            return
        
        print("✅ 页面访问成功")
        
        # 点击下载按钮
        print("点击下载按钮...")
        try:
            save_btn = downloader.driver.find_element(By.CLASS_NAME, "saveas-btn")
            save_btn.click()
            print("✅ 下载按钮已点击")
        except Exception as e:
            print(f"❌ 点击下载按钮失败: {e}")
            downloader.close()
            return
        
        # 等待菜单出现
        time.sleep(3)
        
        # 使用改进的选择器尝试下载
        print("尝试改进的下载选项识别...")
        
        download_success = False
        
        # 策略1: 尝试所有包含"视频"的元素
        try:
            video_elements = downloader.driver.find_elements(By.XPATH, "//*[contains(text(), '视频')]")
            for elem in video_elements:
                if elem.is_displayed() and elem.is_enabled():
                    print(f"尝试点击: {elem.text}")
                    elem.click()
                    download_success = True
                    break
        except Exception as e:
            print(f"策略1失败: {e}")
        
        # 策略2: 如果策略1失败，尝试第一个可见的菜单项
        if not download_success:
            try:
                menu_items = downloader.driver.find_elements(By.XPATH, "//div[contains(@class, 'dropdown')]//div[text()]")
                for item in menu_items:
                    if item.is_displayed() and item.text.strip():
                        print(f"尝试点击菜单项: {item.text}")
                        item.click()
                        download_success = True
                        break
            except Exception as e:
                print(f"策略2失败: {e}")
        
        # 策略3: 尝试所有可见的链接和按钮
        if not download_success:
            try:
                all_clickable = downloader.driver.find_elements(By.XPATH, "//a | //button | //div[@role='button']")
                for elem in all_clickable:
                    if elem.is_displayed() and elem.text.strip() and any(keyword in elem.text.lower() for keyword in ['视频', '音频', '下载', 'video', 'audio', 'download']):
                        print(f"尝试点击: {elem.text}")
                        elem.click()
                        download_success = True
                        break
            except Exception as e:
                print(f"策略3失败: {e}")
        
        if download_success:
            print("✅ 成功选择下载选项")
            print("等待下载开始...")
            time.sleep(5)
            
            # 检查下载
            import os
            files_before = set(os.listdir(downloader.download_dir))
            time.sleep(5)
            files_after = set(os.listdir(downloader.download_dir))
            new_files = files_after - files_before
            
            if new_files:
                print(f"✅ 检测到新文件: {list(new_files)}")
            else:
                print("⚠️ 未检测到新文件，下载可能仍在进行中")
        else:
            print("❌ 未能选择任何下载选项")
            print("请手动在浏览器中选择下载选项")
        
        input("按回车关闭浏览器...")
        downloader.close()
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def main():
    """主函数"""
    print("🔧 下载选项识别修复工具")
    print("=" * 60)
    
    print("选择操作:")
    print("1. 分析下载菜单结构")
    print("2. 测试改进的下载方法")
    print("3. 两个都执行")
    
    choice = input("请选择 (1-3): ").strip()
    
    if choice == '1':
        analyze_download_menu()
    elif choice == '2':
        test_improved_download()
    elif choice == '3':
        analyze_download_menu()
        test_improved_download()
    else:
        print("无效选择")

if __name__ == "__main__":
    main()
