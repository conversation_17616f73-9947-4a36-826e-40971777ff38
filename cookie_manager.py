#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cookie管理器
用于保存和加载腾讯会议的登录cookie
"""

import json
import os
import pickle
from datetime import datetime, timedelta
from pathlib import Path

class CookieManager:
    def __init__(self, cookie_file="tencent_meeting_cookies.json"):
        """
        初始化Cookie管理器
        
        Args:
            cookie_file (str): cookie保存文件名
        """
        self.cookie_file = cookie_file
        self.cookie_path = os.path.join(os.getcwd(), cookie_file)
        
    def save_cookies(self, driver):
        """
        保存浏览器的cookies
        
        Args:
            driver: Selenium WebDriver实例
        """
        try:
            cookies = driver.get_cookies()
            
            # 添加保存时间戳
            cookie_data = {
                'cookies': cookies,
                'saved_time': datetime.now().isoformat(),
                'domain': 'meeting.tencent.com'
            }
            
            with open(self.cookie_path, 'w', encoding='utf-8') as f:
                json.dump(cookie_data, f, ensure_ascii=False, indent=2)
            
            print(f"✓ Cookies已保存到: {self.cookie_path}")
            print(f"✓ 保存了 {len(cookies)} 个cookie")
            return True
            
        except Exception as e:
            print(f"✗ 保存cookies失败: {e}")
            return False
    
    def load_cookies(self, driver):
        """
        加载cookies到浏览器
        
        Args:
            driver: Selenium WebDriver实例
            
        Returns:
            bool: 是否成功加载cookies
        """
        try:
            if not os.path.exists(self.cookie_path):
                print(f"⚠️ Cookie文件不存在: {self.cookie_path}")
                return False
            
            with open(self.cookie_path, 'r', encoding='utf-8') as f:
                cookie_data = json.load(f)
            
            # 检查cookie是否过期（7天）
            saved_time = datetime.fromisoformat(cookie_data['saved_time'])
            if datetime.now() - saved_time > timedelta(days=7):
                print("⚠️ Cookies已过期（超过7天），需要重新登录")
                return False
            
            cookies = cookie_data['cookies']
            
            # 先访问域名
            driver.get("https://meeting.tencent.com")
            
            # 添加cookies
            for cookie in cookies:
                try:
                    # 移除可能导致问题的字段
                    clean_cookie = {
                        'name': cookie['name'],
                        'value': cookie['value'],
                        'domain': cookie.get('domain', '.meeting.tencent.com'),
                        'path': cookie.get('path', '/'),
                    }
                    
                    # 只添加有效的可选字段
                    if 'secure' in cookie:
                        clean_cookie['secure'] = cookie['secure']
                    if 'httpOnly' in cookie:
                        clean_cookie['httpOnly'] = cookie['httpOnly']
                    
                    driver.add_cookie(clean_cookie)
                except Exception as e:
                    print(f"⚠️ 添加cookie失败: {cookie['name']} - {e}")
            
            print(f"✓ 成功加载 {len(cookies)} 个cookie")
            return True
            
        except Exception as e:
            print(f"✗ 加载cookies失败: {e}")
            return False
    
    def is_cookies_valid(self):
        """
        检查cookies是否有效
        
        Returns:
            bool: cookies是否有效
        """
        try:
            if not os.path.exists(self.cookie_path):
                return False
            
            with open(self.cookie_path, 'r', encoding='utf-8') as f:
                cookie_data = json.load(f)
            
            saved_time = datetime.fromisoformat(cookie_data['saved_time'])
            return datetime.now() - saved_time <= timedelta(days=7)
            
        except:
            return False
    
    def clear_cookies(self):
        """清除保存的cookies"""
        try:
            if os.path.exists(self.cookie_path):
                os.remove(self.cookie_path)
                print(f"✓ 已清除cookies文件: {self.cookie_path}")
            else:
                print("⚠️ Cookie文件不存在")
        except Exception as e:
            print(f"✗ 清除cookies失败: {e}")
    
    def get_cookie_info(self):
        """获取cookie信息"""
        try:
            if not os.path.exists(self.cookie_path):
                return None
            
            with open(self.cookie_path, 'r', encoding='utf-8') as f:
                cookie_data = json.load(f)
            
            saved_time = datetime.fromisoformat(cookie_data['saved_time'])
            age = datetime.now() - saved_time
            
            return {
                'file_path': self.cookie_path,
                'saved_time': saved_time,
                'age_days': age.days,
                'cookie_count': len(cookie_data['cookies']),
                'is_valid': age <= timedelta(days=7)
            }
            
        except Exception as e:
            print(f"✗ 获取cookie信息失败: {e}")
            return None

def get_cookies_via_mcp():
    """
    通过MCP Chrome扩展获取当前页面的cookies
    """
    try:
        # 使用chrome-mcp-stdio获取当前页面cookies
        from chrome_network_request_chrome_mcp_stdio import chrome_network_request
        
        # 这里我们需要使用JavaScript来获取cookies
        js_code = """
        // 获取所有cookies
        function getAllCookies() {
            return document.cookie.split(';').map(cookie => {
                const [name, value] = cookie.trim().split('=');
                return { name, value };
            }).filter(cookie => cookie.name && cookie.value);
        }
        
        // 返回cookies信息
        JSON.stringify({
            cookies: getAllCookies(),
            domain: window.location.hostname,
            url: window.location.href,
            timestamp: new Date().toISOString()
        });
        """
        
        return js_code
        
    except Exception as e:
        print(f"✗ MCP获取cookies失败: {e}")
        return None

def main():
    """测试Cookie管理器"""
    print("Cookie管理器测试")
    print("=" * 30)
    
    manager = CookieManager()
    
    # 检查现有cookies
    info = manager.get_cookie_info()
    if info:
        print(f"现有cookies信息:")
        print(f"  文件路径: {info['file_path']}")
        print(f"  保存时间: {info['saved_time']}")
        print(f"  存在天数: {info['age_days']} 天")
        print(f"  Cookie数量: {info['cookie_count']}")
        print(f"  是否有效: {'是' if info['is_valid'] else '否'}")
    else:
        print("未找到现有cookies")
    
    print(f"\nCookie文件路径: {manager.cookie_path}")
    print(f"Cookies是否有效: {'是' if manager.is_cookies_valid() else '否'}")

if __name__ == "__main__":
    main()
