#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断扫码登录后无法下载的问题
"""

import time
import os
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from tencent_meeting_downloader import TencentMeetingDownloader
from cookie_manager import <PERSON>ieManager

def diagnose_page_status(url):
    """诊断页面状态"""
    print("🔍 诊断页面状态")
    print("=" * 50)
    
    try:
        downloader = TencentMeetingDownloader(
            headless=False,  # 显示浏览器以便观察
            use_cookies=True
        )
        
        print(f"正在访问: {url}")
        downloader.driver.get(url)
        
        # 等待页面加载
        print("等待页面加载...")
        time.sleep(5)
        
        # 获取页面标题
        title = downloader.driver.title
        print(f"页面标题: {title}")
        
        # 检查页面源码
        page_source = downloader.driver.page_source
        
        # 1. 检查登录状态
        print("\n1. 检查登录状态:")
        login_indicators = ["登录", "login", "请先登录", "未登录", "sign in"]
        login_found = False
        for indicator in login_indicators:
            if indicator.lower() in page_source.lower():
                print(f"   ⚠️ 发现登录相关内容: {indicator}")
                login_found = True
        
        if not login_found:
            print("   ✓ 未发现登录相关内容")
        
        # 2. 检查权限问题
        print("\n2. 检查权限问题:")
        permission_indicators = ["权限", "permission", "访问被拒绝", "access denied", "无权限", "unauthorized", "forbidden"]
        permission_found = False
        for indicator in permission_indicators:
            if indicator.lower() in page_source.lower():
                print(f"   ⚠️ 发现权限相关内容: {indicator}")
                permission_found = True
        
        if not permission_found:
            print("   ✓ 未发现权限问题")
        
        # 3. 检查另存为按钮
        print("\n3. 检查另存为按钮:")
        try:
            save_btn = downloader.driver.find_element(By.CLASS_NAME, "saveas-btn")
            print("   ✓ 找到另存为按钮")
            print(f"   按钮文本: {save_btn.text}")
            print(f"   按钮可见: {save_btn.is_displayed()}")
            print(f"   按钮可点击: {save_btn.is_enabled()}")
        except NoSuchElementException:
            print("   ✗ 未找到另存为按钮")
            
            # 尝试查找其他可能的按钮
            print("   尝试查找其他下载相关按钮...")
            download_selectors = [
                "//button[contains(text(), '下载')]",
                "//button[contains(text(), '另存为')]", 
                "//a[contains(text(), '下载')]",
                "//div[contains(@class, 'download')]",
                "//div[contains(@class, 'save')]"
            ]
            
            for selector in download_selectors:
                try:
                    elements = downloader.driver.find_elements(By.XPATH, selector)
                    if elements:
                        print(f"   找到相关元素: {selector} (数量: {len(elements)})")
                        for i, elem in enumerate(elements[:3]):  # 只显示前3个
                            print(f"     元素{i+1}: {elem.text[:50]}")
                except:
                    pass
        
        # 4. 检查页面内容
        print("\n4. 检查页面内容:")
        content_indicators = ["录制", "视频", "音频", "转录", "摘要", "recording", "video", "audio"]
        content_found = False
        for indicator in content_indicators:
            if indicator.lower() in page_source.lower():
                print(f"   ✓ 发现内容相关: {indicator}")
                content_found = True
                break
        
        if not content_found:
            print("   ⚠️ 未发现录制内容相关信息")
        
        # 5. 检查错误信息
        print("\n5. 检查错误信息:")
        error_indicators = ["错误", "error", "失败", "failed", "过期", "expired", "不存在", "not found"]
        error_found = False
        for indicator in error_indicators:
            if indicator.lower() in page_source.lower():
                print(f"   ⚠️ 发现错误相关: {indicator}")
                error_found = True
        
        if not error_found:
            print("   ✓ 未发现错误信息")
        
        # 6. 保存页面截图和源码用于分析
        print("\n6. 保存诊断信息:")
        try:
            screenshot_path = "diagnosis_screenshot.png"
            downloader.driver.save_screenshot(screenshot_path)
            print(f"   ✓ 页面截图已保存: {screenshot_path}")
            
            source_path = "diagnosis_page_source.html"
            with open(source_path, "w", encoding="utf-8") as f:
                f.write(page_source)
            print(f"   ✓ 页面源码已保存: {source_path}")
        except Exception as e:
            print(f"   ⚠️ 保存诊断信息失败: {e}")
        
        # 7. 检查cookies状态
        print("\n7. 检查Cookies状态:")
        cookies = downloader.driver.get_cookies()
        print(f"   当前cookies数量: {len(cookies)}")
        
        # 检查重要的cookies
        important_cookies = ["session", "token", "auth", "login", "user"]
        for cookie in cookies:
            cookie_name = cookie['name'].lower()
            for important in important_cookies:
                if important in cookie_name:
                    print(f"   ✓ 找到重要cookie: {cookie['name']}")
                    break
        
        # 保持浏览器打开以便手动检查
        print("\n8. 手动检查:")
        print("   浏览器将保持打开状态，请手动检查页面")
        print("   - 确认是否已登录")
        print("   - 查看是否有下载按钮")
        print("   - 尝试手动点击下载")
        
        input("   检查完成后按回车继续...")
        
        downloader.close()
        return True
        
    except Exception as e:
        print(f"✗ 诊断过程失败: {e}")
        return False

def check_cookie_after_login():
    """检查登录后的cookie状态"""
    print("\n🍪 检查登录后的Cookie状态")
    print("=" * 50)
    
    manager = CookieManager()
    info = manager.get_cookie_info()
    
    if info:
        print(f"✓ Cookie文件存在")
        print(f"  保存时间: {info['saved_time']}")
        print(f"  存在天数: {info['age_days']} 天")
        print(f"  Cookie数量: {info['cookie_count']}")
        print(f"  是否有效: {'是' if info['is_valid'] else '否'}")
        
        # 读取cookie内容
        try:
            import json
            with open(manager.cookie_path, 'r', encoding='utf-8') as f:
                cookie_data = json.load(f)
            
            print(f"\nCookie详情:")
            for i, cookie in enumerate(cookie_data.get('cookies', [])[:5]):  # 只显示前5个
                print(f"  {i+1}. {cookie.get('name', 'unknown')}: {cookie.get('value', '')[:20]}...")
                
        except Exception as e:
            print(f"  ⚠️ 读取cookie详情失败: {e}")
    else:
        print("✗ 未找到cookie文件")
        print("建议:")
        print("1. 重新运行登录流程")
        print("2. 确保在登录成功后保存了cookies")

def provide_solutions():
    """提供解决方案"""
    print("\n💡 可能的解决方案")
    print("=" * 50)
    
    print("根据常见问题，请尝试以下解决方案:")
    
    print("\n1. 🔐 登录相关问题:")
    print("   - 确保扫码登录成功")
    print("   - 登录后访问录制文件页面确认可以看到内容")
    print("   - 重新保存cookies: python complete_download_with_cookies.py")
    
    print("\n2. 🔑 权限问题:")
    print("   - 确认您有访问该录制文件的权限")
    print("   - 尝试在浏览器中手动访问链接")
    print("   - 联系会议组织者确认权限")
    
    print("\n3. 🌐 页面加载问题:")
    print("   - 检查网络连接")
    print("   - 清除浏览器缓存")
    print("   - 尝试使用不同的浏览器")
    
    print("\n4. 🔧 技术问题:")
    print("   - 更新Chrome浏览器到最新版本")
    print("   - 重新安装ChromeDriver")
    print("   - 检查防火墙和安全软件设置")
    
    print("\n5. 📝 录制文件问题:")
    print("   - 确认录制文件确实存在")
    print("   - 检查录制文件是否已过期")
    print("   - 尝试访问其他录制文件")

def main():
    """主诊断流程"""
    print("🔍 腾讯会议下载问题诊断工具")
    print("=" * 60)
    
    # 获取要诊断的URL
    print("请输入要诊断的腾讯会议录制文件URL:")
    url = input("URL: ").strip()
    
    if not url:
        print("使用默认测试URL")
        url = "https://meeting.tencent.com/cw/lvm1Z7Gj4f"
    
    print(f"\n开始诊断URL: {url}")
    
    # 1. 检查cookie状态
    check_cookie_after_login()
    
    # 2. 诊断页面状态
    diagnose_page_status(url)
    
    # 3. 提供解决方案
    provide_solutions()
    
    print("\n" + "=" * 60)
    print("诊断完成！")
    print("如果问题仍然存在，请:")
    print("1. 查看保存的截图和页面源码")
    print("2. 尝试提供的解决方案")
    print("3. 联系技术支持并提供诊断信息")

if __name__ == "__main__":
    main()
