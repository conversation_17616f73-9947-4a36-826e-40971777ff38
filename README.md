# 腾讯会议录制内容下载器

这是一个用于自动下载腾讯会议录制内容的Python工具，支持下载视频、音频、转写文本和会议纪要等多种格式。

## 功能特性

- ✅ 下载视频内容（MP4格式）
- ✅ 下载纯音频文件
- ✅ 下载转写文本（原文版和智能优化版）
- ✅ 下载会议纪要（主题摘要、分章节小结、发言人观点、DeepSeek摘要）
- ✅ 支持批量下载多种格式
- ✅ 自动等待下载完成
- ✅ 支持自定义下载目录
- ✅ 支持无头模式运行

## 环境要求

- Python 3.7+
- Chrome浏览器
- ChromeDriver（会自动下载）

## 安装步骤

### 1. 克隆或下载代码

```bash
# 如果使用git
git clone <repository-url>
cd 腾讯会议视频下载

# 或者直接下载文件到本地目录
```

### 2. 安装Python依赖

```bash
pip install -r requirements.txt
```

### 3. 安装ChromeDriver

ChromeDriver会在首次运行时自动下载，或者您可以手动安装：

**Windows:**
```bash
# 使用chocolatey
choco install chromedriver

# 或者手动下载
# 1. 访问 https://chromedriver.chromium.org/
# 2. 下载对应Chrome版本的驱动
# 3. 将chromedriver.exe放到PATH环境变量中
```

**macOS:**
```bash
# 使用homebrew
brew install chromedriver
```

**Linux:**
```bash
# Ubuntu/Debian
sudo apt-get install chromium-chromedriver

# 或者手动下载安装
```

## 使用方法

### 🔐 Cookie认证设置（重要）

由于腾讯会议录制文件通常需要登录权限，建议先设置cookie认证：

#### 方法一：自动登录模式（推荐）
```bash
python complete_download_with_cookies.py
```
程序会自动打开浏览器，您只需：
1. 登录腾讯会议账号
2. 访问录制文件页面
3. 确认可以正常访问后按回车
4. 程序自动保存登录状态

#### 方法二：手动获取Cookie
```bash
python mcp_cookie_extractor.py
```
按照提示在浏览器中手动获取cookie数据

### 快速开始

1. **设置认证**: 首先运行cookie认证设置（见上方说明）

2. **修改URL**: 编辑 `download_example.py` 文件，将 `meeting_url` 替换为您的腾讯会议录制页面URL

3. **运行下载脚本**:
```bash
python download_example.py
```

### 自定义下载

```python
from tencent_meeting_downloader import TencentMeetingDownloader

# 创建下载器
downloader = TencentMeetingDownloader(
    headless=False,  # 是否无头模式
    download_dir=None  # 使用当前目录（默认）
)

# 指定要下载的内容
content_types = [
    'video',                # 视频内容
    'audio',                # 音频文件
    'transcript_original',  # 原文转写
    'summary_topic'         # 主题摘要
]

# 执行下载
downloader.download_all_content(meeting_url, content_types)
downloader.close()
```

### 支持的内容类型

| 类型 | 说明 |
|------|------|
| `video` | 视频内容（MP4格式） |
| `audio` | 纯音频文件 |
| `transcript_original` | 原文版转写文本 |
| `transcript_optimized` | 智能优化版转写文本 |
| `summary_topic` | 主题摘要 |
| `summary_chapter` | 分章节小结 |
| `summary_speaker` | 发言人观点 |
| `summary_deepseek` | DeepSeek摘要 |

## 配置选项

### TencentMeetingDownloader 参数

- `headless` (bool): 是否使用无头模式运行浏览器，默认False
- `download_dir` (str): 下载目录路径，默认为当前目录

### 下载方法

- `download_all_content(url, content_types)`: 批量下载指定类型的内容
- `download_video_content()`: 单独下载视频
- `download_audio_content()`: 单独下载音频
- `download_transcript(version)`: 下载转写文本
- `download_summary(summary_type)`: 下载会议纪要

## 使用示例

### 示例1: 下载所有内容

```python
from tencent_meeting_downloader import TencentMeetingDownloader

url = "https://meeting.tencent.com/cw/your-meeting-id"
downloader = TencentMeetingDownloader()

# 下载所有类型的内容
all_types = [
    'video', 'audio', 'transcript_original', 'transcript_optimized',
    'summary_topic', 'summary_chapter', 'summary_speaker', 'summary_deepseek'
]

downloader.download_all_content(url, all_types)
downloader.close()
```

### 示例2: 只下载视频和音频

```python
downloader = TencentMeetingDownloader()
downloader.download_all_content(url, ['video', 'audio'])
downloader.close()
```

### 示例3: 只下载文本内容

```python
downloader = TencentMeetingDownloader()
text_types = ['transcript_original', 'summary_topic']
downloader.download_all_content(url, text_types)
downloader.close()
```

## 注意事项

1. **权限要求**: 确保您有访问该会议录制的权限
2. **网络连接**: 需要稳定的网络连接，大文件下载可能需要较长时间
3. **存储空间**: 确保有足够的磁盘空间存储下载的文件
4. **浏览器版本**: 确保Chrome浏览器是最新版本
5. **下载限制**: 某些会议可能有下载次数或时间限制

## 故障排除

### 常见问题

1. **ChromeDriver版本不匹配**
   ```
   解决方案: 更新Chrome浏览器或下载匹配的ChromeDriver版本
   ```

2. **页面加载超时**
   ```
   解决方案: 检查网络连接，确认URL是否正确
   ```

3. **找不到下载按钮**
   ```
   解决方案: 确认您有访问权限，页面是否完全加载
   ```

4. **下载失败**
   ```
   解决方案: 检查磁盘空间，确认下载目录权限
   ```

### 调试模式

设置 `headless=False` 可以看到浏览器操作过程，便于调试：

```python
downloader = TencentMeetingDownloader(headless=False)
```

## 免责声明

本工具仅供学习和个人使用，请遵守腾讯会议的使用条款和相关法律法规。用户应确保有合法权限下载相关内容。

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个工具。
